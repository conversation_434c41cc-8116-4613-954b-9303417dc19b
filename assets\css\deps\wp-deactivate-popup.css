.cardView{
    max-width:100% !important;
    margin-top:10px !important;
    margin-bottom:20px !important;
    border-radius:5px !important;
}

.myModal{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  opacity: 1;
  z-index: 100000;
}

/* .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 1rem 1.5rem;
  width: 24rem;
  border-radius: 0.5rem;
  width: 50%;
  max-height: 70%;
  z-index: 10;
  overflow: auto;
} */

/* .close-button {
  float: right;
  width: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
  cursor: pointer;
  border-radius: 0.25rem;
  background-color: lightgray;
}
.close-button:hover {
  background-color: darkgray;
}  */

#loader {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: rgba(0,0,0,0.75) url(../../images/loading.gif) no-repeat center center;
  z-index: 1000000;
}