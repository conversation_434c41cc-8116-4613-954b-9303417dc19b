<?php declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON>\Parser;

use Php<PERSON><PERSON><PERSON>\Error;
use Php<PERSON>arser\Modifiers;
use Php<PERSON>arser\Node;
use PhpParser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use Php<PERSON><PERSON>er\Node\Scalar;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar file grammar/php.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    public const YYERRTOK = 256;
    public const T_THROW = 257;
    public const T_INCLUDE = 258;
    public const T_INCLUDE_ONCE = 259;
    public const T_EVAL = 260;
    public const T_REQUIRE = 261;
    public const T_REQUIRE_ONCE = 262;
    public const T_LOGICAL_OR = 263;
    public const T_LOGICAL_XOR = 264;
    public const T_LOGICAL_AND = 265;
    public const T_PRINT = 266;
    public const T_YIELD = 267;
    public const T_DOUBLE_ARROW = 268;
    public const T_YIELD_FROM = 269;
    public const T_PLUS_EQUAL = 270;
    public const T_MINUS_EQUAL = 271;
    public const T_MUL_EQUAL = 272;
    public const T_DIV_EQUAL = 273;
    public const T_CONCAT_EQUAL = 274;
    public const T_MOD_EQUAL = 275;
    public const T_AND_EQUAL = 276;
    public const T_OR_EQUAL = 277;
    public const T_XOR_EQUAL = 278;
    public const T_SL_EQUAL = 279;
    public const T_SR_EQUAL = 280;
    public const T_POW_EQUAL = 281;
    public const T_COALESCE_EQUAL = 282;
    public const T_COALESCE = 283;
    public const T_BOOLEAN_OR = 284;
    public const T_BOOLEAN_AND = 285;
    public const T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG = 286;
    public const T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG = 287;
    public const T_IS_EQUAL = 288;
    public const T_IS_NOT_EQUAL = 289;
    public const T_IS_IDENTICAL = 290;
    public const T_IS_NOT_IDENTICAL = 291;
    public const T_SPACESHIP = 292;
    public const T_IS_SMALLER_OR_EQUAL = 293;
    public const T_IS_GREATER_OR_EQUAL = 294;
    public const T_SL = 295;
    public const T_SR = 296;
    public const T_INSTANCEOF = 297;
    public const T_INC = 298;
    public const T_DEC = 299;
    public const T_INT_CAST = 300;
    public const T_DOUBLE_CAST = 301;
    public const T_STRING_CAST = 302;
    public const T_ARRAY_CAST = 303;
    public const T_OBJECT_CAST = 304;
    public const T_BOOL_CAST = 305;
    public const T_UNSET_CAST = 306;
    public const T_POW = 307;
    public const T_NEW = 308;
    public const T_CLONE = 309;
    public const T_EXIT = 310;
    public const T_IF = 311;
    public const T_ELSEIF = 312;
    public const T_ELSE = 313;
    public const T_ENDIF = 314;
    public const T_LNUMBER = 315;
    public const T_DNUMBER = 316;
    public const T_STRING = 317;
    public const T_STRING_VARNAME = 318;
    public const T_VARIABLE = 319;
    public const T_NUM_STRING = 320;
    public const T_INLINE_HTML = 321;
    public const T_ENCAPSED_AND_WHITESPACE = 322;
    public const T_CONSTANT_ENCAPSED_STRING = 323;
    public const T_ECHO = 324;
    public const T_DO = 325;
    public const T_WHILE = 326;
    public const T_ENDWHILE = 327;
    public const T_FOR = 328;
    public const T_ENDFOR = 329;
    public const T_FOREACH = 330;
    public const T_ENDFOREACH = 331;
    public const T_DECLARE = 332;
    public const T_ENDDECLARE = 333;
    public const T_AS = 334;
    public const T_SWITCH = 335;
    public const T_MATCH = 336;
    public const T_ENDSWITCH = 337;
    public const T_CASE = 338;
    public const T_DEFAULT = 339;
    public const T_BREAK = 340;
    public const T_CONTINUE = 341;
    public const T_GOTO = 342;
    public const T_FUNCTION = 343;
    public const T_FN = 344;
    public const T_CONST = 345;
    public const T_RETURN = 346;
    public const T_TRY = 347;
    public const T_CATCH = 348;
    public const T_FINALLY = 349;
    public const T_USE = 350;
    public const T_INSTEADOF = 351;
    public const T_GLOBAL = 352;
    public const T_STATIC = 353;
    public const T_ABSTRACT = 354;
    public const T_FINAL = 355;
    public const T_PRIVATE = 356;
    public const T_PROTECTED = 357;
    public const T_PUBLIC = 358;
    public const T_READONLY = 359;
    public const T_VAR = 360;
    public const T_UNSET = 361;
    public const T_ISSET = 362;
    public const T_EMPTY = 363;
    public const T_HALT_COMPILER = 364;
    public const T_CLASS = 365;
    public const T_TRAIT = 366;
    public const T_INTERFACE = 367;
    public const T_ENUM = 368;
    public const T_EXTENDS = 369;
    public const T_IMPLEMENTS = 370;
    public const T_OBJECT_OPERATOR = 371;
    public const T_NULLSAFE_OBJECT_OPERATOR = 372;
    public const T_LIST = 373;
    public const T_ARRAY = 374;
    public const T_CALLABLE = 375;
    public const T_CLASS_C = 376;
    public const T_TRAIT_C = 377;
    public const T_METHOD_C = 378;
    public const T_FUNC_C = 379;
    public const T_LINE = 380;
    public const T_FILE = 381;
    public const T_START_HEREDOC = 382;
    public const T_END_HEREDOC = 383;
    public const T_DOLLAR_OPEN_CURLY_BRACES = 384;
    public const T_CURLY_OPEN = 385;
    public const T_PAAMAYIM_NEKUDOTAYIM = 386;
    public const T_NAMESPACE = 387;
    public const T_NS_C = 388;
    public const T_DIR = 389;
    public const T_NS_SEPARATOR = 390;
    public const T_ELLIPSIS = 391;
    public const T_NAME_FULLY_QUALIFIED = 392;
    public const T_NAME_QUALIFIED = 393;
    public const T_NAME_RELATIVE = 394;
    public const T_ATTRIBUTE = 395;

    protected int $tokenToSymbolMapSize = 396;
    protected int $actionTableSize = 1258;
    protected int $gotoTableSize = 567;

    protected int $invalidSymbol = 168;
    protected int $errorSymbol = 1;
    protected int $defaultAction = -32766;
    protected int $unexpectedTokenRule = 32767;

    protected int $YY2TBLSTATE = 435;
    protected int $numNonLeafStates = 739;

    protected array $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'('",
        "')'",
        "'{'",
        "'}'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected array $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  166,  168,  167,   55,  168,  168,
          161,  162,   53,   50,    8,   51,   52,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  159,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  160,   36,  168,  165,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  163,   35,  164,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   48,   49,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158
    );

    protected array $action = array(
          133,  134,  135,  582,  136,  137,    0,  751,  752,  753,
          138,   38,-32766,-32766,-32766,  151,-32766,-32766,-32766,-32767,
        -32767,-32767,-32767,  102,  103,  104,  105,  106, 1112, 1113,
         1114, 1111, 1110, 1109, 1115,  745,  744,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767, 1245,  837,-32766, 1322,  754,-32766,-32766,-32766,-32766,
         -594,-32766,-32766,-32766,  104,  105,  106, -594, 1306,  265,
          139,  404,  758,  759,  760,  761,  990,-32766,  429,-32766,
        -32766,  -16,-32766,  242, 1027,  815,  762,  763,  764,  765,
          766,  767,  768,  769,  770,  771,  791,  583,  792,  793,
          794,  795,  783,  784,  345,  346,  786,  787,  772,  773,
          774,  776,  777,  778,  356,  818,  819,  820,  821,  822,
          584,  779,  780,  585,  586,-32766,  803,  801,  802,  814,
          798,  799,  835,  826,  587,  588,  797,  589,  590,  591,
          592,  593,  594,  826,  459,  460,  461, 1036,  800,  595,
          596,  941,  140,    2,  133,  134,  135,  582,  136,  137,
         1060,  751,  752,  753,  138,   38, -328, -110, -110, 1326,
          290,   23, -110,-32766,-32766,-32766, 1325,   35, -110, 1112,
         1113, 1114, 1111, 1110, 1109, 1115,  612,-32766,  129,  745,
          744,  107,  108,  109,-32766,  274,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,  828,  991, -194,  145,  110,  298,  754,
          836,   75,-32766,-32766,-32766, 1351,  142,  326, 1352, -594,
          326, -594,  254,  265,  139,  404,  758,  759,  760,  761,
           82, -272,  429,-32766,  326,-32766,-32766,-32766,-32766,  815,
          762,  763,  764,  765,  766,  767,  768,  769,  770,  771,
          791,  583,  792,  793,  794,  795,  783,  784,  345,  346,
          786,  787,  772,  773,  774,  776,  777,  778,  356,  818,
          819,  820,  821,  822,  584,  779,  780,  585,  586,  830,
          803,  801,  802,  814,  798,  799,  712,  309,  587,  588,
          797,  589,  590,  591,  592,  593,  594,  -78,   83,   84,
           85,  -85,  800,  595,  596,  311,  149,  775,  746,  747,
          748,  749,  750,  725,  751,  752,  753,  788,  789,   37,
         -328,   86,   87,   88,   89,   90,   91,   92,   93,   94,
           95,   96,   97,   98,   99,  100,  101,  102,  103,  104,
          105,  106,  107,  108,  109,  323,  274,  482,-32766,-32766,
        -32766,  -58,-32766,-32766,-32766,  959,  960,  127,  110, -194,
          961,  339,  754,-32766,-32766,-32766,  955,  -85,  291,-32766,
         1088,-32766,-32766,-32766,-32766,-32766,  755,  756,  757,  758,
          759,  760,  761, -193,-32766,  824,-32766,-32766,-32766, -367,
          429, -367,  815,  762,  763,  764,  765,  766,  767,  768,
          769,  770,  771,  791,  813,  792,  793,  794,  795,  783,
          784,  785,  812,  786,  787,  772,  773,  774,  776,  777,
          778,  817,  818,  819,  820,  821,  822,  823,  779,  780,
          781,  782, -548,  803,  801,  802,  814,  798,  799,  340,
          327,  790,  796,  797,  804,  805,  807,  806,  808,  809,
         1033,  391,  606,    7,-32766,  800,  811,  810,   50,   51,
           52,  513,   53,   54,  831, 1240, 1239, 1241,   55,   56,
         -110,   57, 1036,  920, 1090, -110, 1036, -110,  291,  483,
          745,  744,  305,  382,  381, -110, -110, -110, -110, -110,
         -110, -110, -110,  423,  920,  283, -548, -548,  152,  290,
          380,  381, 1245,  715,  467,  468,   58,   59,  370,   21,
          423, -545,   60,  556,   61,  248,  249,   62,   63,   64,
           65,   66,   67,   68,   69, -548,   28,  267,   70,  445,
          514, 1104,  374, -342, 1272, 1273,  515, -193,  835,  154,
          832, -544, 1270,   42,   25,  516,  389,  517,  241,  518,
          920,  519,  298, 1238,  520,  521,  910,  920,  441,   44,
           45,  446,  377,  376,-32766,   46,  522, 1023, 1022, 1021,
         1024,  368,  338,  442, 1278, -545, -545,  910, 1231,  443,
          524,  525,  526,  835, 1245,  835, 1036,  716, 1341, 1236,
         -545,  155,  528,  529,-32766, 1259, 1260, 1261, 1262, 1256,
         1257,  297, -551,  943, -545, -544, -544, 1263, 1258,  290,
         1035, 1240, 1239, 1241,  298,  444, 1036,   71, 1266,  841,
         -544,  321,  322,  326, -153, -153, -153,  920, 1240, 1239,
         1241,  922, -550,  910, -544,  710,  943, -591,-32766, -153,
          910, -153,  357, -153, -591, -153,  862, 1033,  863, 1089,
           36,  251,  922,  737,  156,  375,  710,  717,  862, -585,
          863, -585,   75,  158, -546,  835,  959,  960,  326, 1036,
          -57,  523,  920,-32766,-32766,  362,  896,  955, -110, -110,
         -110,   32,  111,  112,  113,  114,  115,  116,  117,  118,
          119,  120,  121,  122,  123,  745,  744,  656,   26,  835,
         -110, -110,  720,  745,  744, -110,   33,  834,  922,  124,
          910, -110,  710, -153,  125,  922,  675,  676,  130,  710,
        -32766,  150,  407,  131, 1150, 1152,   48,  144, -546, -546,
          378,  379,-32766,  383,  384, -543,   28,  159, 1238,  920,
          160,  298, 1059, -546,   75,-32766,-32766,-32766,  835,-32766,
          326,-32766, 1270,-32766,  -87,  910,-32766, -546,  647,  648,
          161,-32766,-32766,-32766,   -4,  920,  -84,-32766,-32766,  727,
          162,  287,  163,-32766,  420, -302,  -78,  -73,  -72,  -71,
          141,  287,-32766,  -70,  326,  976,  745,  744, 1231,  710,
          299,  300,  -69,  -68,  -67, -298, -591,  -66, -591, -543,
         -543,  -65,  528,  529,  -46, 1259, 1260, 1261, 1262, 1256,
         1257,  -18,   74,  148, -543,  273,  284, 1263, 1258,  126,
         -543,  726,  910,-32766,  729,  919,  147,   73, -543, 1238,
          922,  690,  322,  326,  710,  279,-32766,-32766,-32766,  280,
        -32766,  285,-32766,  286,-32766,  332,  288,-32766,  910,  289,
          292,   49,-32766,-32766,-32766,  293,  274, 1033,-32766,-32766,
          937,  110,  -50,  685,-32766,  420,  146,  691,  826,  701,
          375,  703,  436,-32766, 1353,   20,  561,  296,  645, 1036,
          835,  959,  960, 1119, -543, -543,  523,-32766,  692,  693,
          306,  527,  955, -110, -110, -110,  132,  922,  834, -543,
          464,  710,  283,  662,  657,-32766, 1240, 1239, 1241,  678,
          304, 1238,  283, -543,   10,  301,  302,  493,-32766,-32766,
        -32766,  663,-32766,  922,-32766,  679,-32766,  710,   -4,-32766,
          373,   40, -508,  956,-32766,-32766,-32766, -275,  731,-32766,
        -32766,-32766,  920,  303,  128, 1238,-32766,  420,  310,    0,
          567,    0,-32766,-32766,-32766,-32766,-32766,    0,-32766,    0,
        -32766,-32766,    0,-32766,    0, 1277, -498,    0,-32766,-32766,
        -32766,-32766, 1279,    0,-32766,-32766,    8, 1238,   24,  372,
        -32766,  420,  920, 1267,-32766,-32766,-32766,  610,-32766,-32766,
        -32766,  939,-32766,  298, -579,-32766,  846,   41,  734,  488,
        -32766,-32766,-32766,-32766,  735,  854,-32766,-32766,  901, 1238,
          574, 1000,-32766,  420,  977,  984,-32766,-32766,-32766,  974,
        -32766,-32766,-32766,  985,-32766,  910,  899,-32766,  972, 1093,
         1096, 1097,-32766,-32766,-32766, 1094, 1095, 1101,-32766,-32766,
         1292, -250, -250, -250,-32766,  420, 1310,  375, 1344,  650,
           28,  267, -578,-32766, -577, -551, -550, -549,  959,  960,
         -492,    1,  835,  523,   29,  910, 1270,   30,  896,  955,
         -110, -110, -110,   39,   43,   47,   72,   76,   77,   78,
           79, -249, -249, -249,   80,   81,  143,  375,  153,  157,
          897,  247,  328,  357,  358,  359,  360,  361,  959,  960,
          922,  362, 1231,  523,  710, -250,  363,  364,  896,  955,
         -110, -110, -110,  365,  366,  367,  369,  529,   28, 1259,
         1260, 1261, 1262, 1256, 1257,  437,  555, 1348, -273, -272,
          835, 1263, 1258,   13, 1270,   14,-32766,   15,   16,   18,
          922,   73, 1238, 1350,  710, -249,  322,  326,  406,-32766,
        -32766,-32766,  484,-32766,  485,-32766,  492,-32766,  495,  496,
        -32766,  497,  498,  502,  503,-32766,-32766,-32766,  504,  511,
         1231,-32766,-32766,  572,  696, 1249, 1190,-32766,  420, 1268,
         1062, 1061, 1042, 1226, 1038,  529,-32766, 1259, 1260, 1261,
         1262, 1256, 1257, -277, -102,   12,   17,   27,  295, 1263,
         1258,  405,  603,  607,  636,  702, 1194, 1244, 1191,   73,
           34, 1323,    0,  320,  322,  326,  371,  711,  714,  718,
          719,  721,  722,  723,  724,    0,  728,  713,    0,  857,
          856,  865,  949,  992,  864, 1349,  948,  946,  947,  950,
         1222,  930,  940,  928,  982,  983,  634, 1347, 1304, 1293,
         1311, 1320,    0, 1207,    0, 1271,    0,  326
    );

    protected array $actionCheck = array(
            2,    3,    4,    5,    6,    7,    0,    9,   10,   11,
           12,   13,    9,   10,   11,   14,    9,   10,   11,   44,
           45,   46,   47,   48,   49,   50,   51,   52,  116,  117,
          118,  119,  120,  121,  122,   37,   38,   30,  116,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,    1,    1,    9,    1,   57,    9,   10,   11,  137,
            1,    9,   10,   11,   50,   51,   52,    8,    1,   71,
           72,   73,   74,   75,   76,   77,   31,   30,   80,   32,
           33,   31,   30,   14,    1,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  116,  128,  129,  130,  131,
          132,  133,   82,   80,  136,  137,  138,  139,  140,  141,
          142,  143,  144,   80,  129,  130,  131,  138,  150,  151,
          152,    1,  154,    8,    2,    3,    4,    5,    6,    7,
          162,    9,   10,   11,   12,   13,    8,  117,  118,    1,
          161,    8,  122,    9,   10,   11,    8,    8,  128,  116,
          117,  118,  119,  120,  121,  122,   51,  137,    8,   37,
           38,   53,   54,   55,   30,   57,   32,   33,   34,   35,
           36,   37,   38,   80,  159,    8,    8,   69,  158,   57,
          159,  161,    9,   10,   11,   80,  163,  167,   83,  160,
          167,  162,    8,   71,   72,   73,   74,   75,   76,   77,
          163,  162,   80,   30,  167,   32,   33,   34,   35,   87,
           88,   89,   90,   91,   92,   93,   94,   95,   96,   97,
           98,   99,  100,  101,  102,  103,  104,  105,  106,  107,
          108,  109,  110,  111,  112,  113,  114,  115,  116,  117,
          118,  119,  120,  121,  122,  123,  124,  125,  126,  156,
          128,  129,  130,  131,  132,  133,  163,    8,  136,  137,
          138,  139,  140,  141,  142,  143,  144,   16,    9,   10,
           11,   31,  150,  151,  152,    8,  154,    2,    3,    4,
            5,    6,    7,  163,    9,   10,   11,   12,   13,   30,
          162,   32,   33,   34,   35,   36,   37,   38,   39,   40,
           41,   42,   43,   44,   45,   46,   47,   48,   49,   50,
           51,   52,   53,   54,   55,    8,   57,   31,    9,   10,
           11,   16,    9,   10,   11,  117,  118,   14,   69,  162,
          122,    8,   57,    9,   10,   11,  128,   97,   30,   30,
            1,   32,   33,   34,   35,   36,   71,   72,   73,   74,
           75,   76,   77,    8,   30,   80,   32,   33,   34,  106,
           80,  108,   87,   88,   89,   90,   91,   92,   93,   94,
           95,   96,   97,   98,   99,  100,  101,  102,  103,  104,
          105,  106,  107,  108,  109,  110,  111,  112,  113,  114,
          115,  116,  117,  118,  119,  120,  121,  122,  123,  124,
          125,  126,   70,  128,  129,  130,  131,  132,  133,    8,
           70,  136,  137,  138,  139,  140,  141,  142,  143,  144,
          116,  106,    1,  108,  116,  150,  151,  152,    2,    3,
            4,    5,    6,    7,   80,  155,  156,  157,   12,   13,
          101,   15,  138,    1,  164,  106,  138,  108,   30,  163,
           37,   38,  113,  106,  107,  116,  117,  118,  119,  120,
          121,  122,  123,  116,    1,  161,  134,  135,   14,  161,
          106,  107,    1,   31,  134,  135,   50,   51,    8,  101,
          116,   70,   56,   85,   58,   59,   60,   61,   62,   63,
           64,   65,   66,   67,   68,  163,   70,   71,   72,   73,
           74,  123,    8,  164,   78,   79,   80,  162,   82,   14,
          156,   70,   86,   87,   88,   89,    8,   91,   97,   93,
            1,   95,  158,   80,   98,   99,   84,    1,    8,  103,
          104,  105,  106,  107,  116,  109,  110,  119,  120,  121,
          122,  115,  116,    8,  146,  134,  135,   84,  122,    8,
          124,  125,  126,   82,    1,   82,  138,   31,   85,  116,
          149,   14,  136,  137,  116,  139,  140,  141,  142,  143,
          144,  145,  161,  122,  163,  134,  135,  151,  152,  161,
          137,  155,  156,  157,  158,    8,  138,  161,    1,    8,
          149,  165,  166,  167,   75,   76,   77,    1,  155,  156,
          157,  159,  161,   84,  163,  163,  122,    1,  137,   90,
           84,   92,  161,   94,    8,   96,  106,  116,  108,  159,
          147,  148,  159,  163,   14,  106,  163,   31,  106,  160,
          108,  162,  161,   14,   70,   82,  117,  118,  167,  138,
           16,  122,    1,    9,   10,  161,  127,  128,  129,  130,
          131,   16,   17,   18,   19,   20,   21,   22,   23,   24,
           25,   26,   27,   28,   29,   37,   38,   75,   76,   82,
          117,  118,   31,   37,   38,  122,   14,  155,  159,   16,
           84,  128,  163,  164,   16,  159,   75,   76,   16,  163,
          137,  101,  102,   16,   59,   60,   70,   16,  134,  135,
          106,  107,   74,  106,  107,   70,   70,   16,   80,    1,
           16,  158,    1,  149,  161,   87,   88,   89,   82,   91,
          167,   93,   86,   95,   31,   84,   98,  163,  111,  112,
           16,  103,  104,  105,    0,    1,   31,  109,  110,   31,
           16,   30,   16,  115,  116,   35,   31,   31,   31,   31,
          163,   30,  124,   31,  167,  159,   37,   38,  122,  163,
          134,  135,   31,   31,   31,   35,  160,   31,  162,  134,
          135,   31,  136,  137,   31,  139,  140,  141,  142,  143,
          144,   31,  154,   31,  149,   31,   31,  151,  152,  163,
           70,   31,   84,   74,   31,   31,   31,  161,  163,   80,
          159,   80,  166,  167,  163,   35,   87,   88,   89,   35,
           91,   35,   93,   35,   95,   35,   37,   98,   84,   37,
           37,   70,  103,  104,  105,   37,   57,  116,  109,  110,
           38,   69,   31,   77,  115,  116,   70,  116,   80,   80,
          106,   92,  108,  124,   83,   97,   89,  113,  113,  138,
           82,  117,  118,   82,  134,  135,  122,   85,  137,  138,
          114,  127,  128,  129,  130,  131,   31,  159,  155,  149,
           97,  163,  161,   96,   90,   74,  155,  156,  157,   94,
          133,   80,  161,  163,  150,  134,  135,   97,   87,   88,
           89,  100,   91,  159,   93,  100,   95,  163,  164,   98,
          149,  159,  149,  128,  103,  104,  105,  162,  164,   74,
          109,  110,    1,  132,  163,   80,  115,  116,  132,   -1,
          153,   -1,   87,   88,   89,  124,   91,   -1,   93,   -1,
           95,  137,   -1,   98,   -1,  146,  149,   -1,  103,  104,
          105,   74,  146,   -1,  109,  110,  149,   80,  149,  149,
          115,  116,    1,  160,   87,   88,   89,  153,   91,  124,
           93,  154,   95,  158,  161,   98,  160,  159,  159,  102,
          103,  104,  105,   74,  159,  159,  109,  110,  159,   80,
           81,  159,  115,  116,  159,  159,   87,   88,   89,  159,
           91,  124,   93,  159,   95,   84,  159,   98,  159,  159,
          159,  159,  103,  104,  105,  159,  159,  159,  109,  110,
          160,  100,  101,  102,  115,  116,  160,  106,  160,  160,
           70,   71,  161,  124,  161,  161,  161,  161,  117,  118,
          161,  161,   82,  122,  161,   84,   86,  161,  127,  128,
          129,  130,  131,  161,  161,  161,  161,  161,  161,  161,
          161,  100,  101,  102,  161,  161,  161,  106,  161,  161,
          164,  161,  161,  161,  161,  161,  161,  161,  117,  118,
          159,  161,  122,  122,  163,  164,  161,  161,  127,  128,
          129,  130,  131,  161,  161,  161,  161,  137,   70,  139,
          140,  141,  142,  143,  144,  161,  161,  164,  162,  162,
           82,  151,  152,  162,   86,  162,   74,  162,  162,  162,
          159,  161,   80,  164,  163,  164,  166,  167,  162,   87,
           88,   89,  162,   91,  162,   93,  162,   95,  162,  162,
           98,  162,  162,  162,  162,  103,  104,  105,  162,  162,
          122,  109,  110,  162,  162,  162,  162,  115,  116,  162,
          162,  162,  162,  162,  162,  137,  124,  139,  140,  141,
          142,  143,  144,  162,  162,  162,  162,  162,  162,  151,
          152,  162,  162,  162,  162,  162,  162,  162,  162,  161,
          163,  162,   -1,  163,  166,  167,  163,  163,  163,  163,
          163,  163,  163,  163,  163,   -1,  163,  163,   -1,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,   -1,  165,   -1,  166,   -1,  167
    );

    protected array $actionBase = array(
            0,   -2,  152,  549,  764,  941,  981,  751,  617,  310,
          123,  877,  556,  671,  671,  738,  671,  472,  626,  789,
           63,  305,  305,  789,  305,  493,  493,  493,  658,  658,
          658,  658,  749,  749,  897,  897,  929,  865,  831, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062,   51,   45,  451,  692, 1036, 1044,
         1040, 1045, 1034, 1033, 1039, 1041, 1046, 1083, 1084,  795,
         1085, 1086, 1082, 1087, 1042,  889, 1035, 1043,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,   44,  343,  664,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,   52,   52,
           52,  666,  666,   47,  354,  980,  203, 1048, 1048, 1048,
         1048, 1048, 1048, 1048, 1048, 1048,  665,  339,  164,  164,
            7,    7,    7,    7,    7,   50,  369,  583,  -25,  -25,
          -25,  -25,  448,  741,  501,  408,  283,  338,  394,  334,
          334,   14,   14,  531,  531,    9,    9,  531,  531,  531,
          478,  478,  478,  478,  441,  471,  552,  428,  824,   53,
           53,   53,   53,  824,  824,  824,  824,  826, 1089,  824,
          824,  824,  594,  750,  750,  781,  138,  138,  138,  750,
          540,  503,  503,  540,  238,  503,   67,  135,  -78,  805,
          377,  499,  -78,  362,  656,  636,   59,  743,  624,  743,
         1032,  481,  802,  802,  514,  773,  746,  878, 1064, 1049,
          821, 1080,  825, 1081,   15,  370,  745, 1031, 1031, 1031,
         1031, 1031, 1031, 1031, 1031, 1031, 1031, 1031, 1090,  443,
         1032,  384, 1090, 1090, 1090,  443,  443,  443,  443,  443,
          443,  443,  443,  443,  443,  647,  384,  622,  641,  384,
          810,  443,   51,  817,   51,   51,   51,   51,   51,   51,
           51,   51,   51,   51,  780,  316,   51,   45,  150,  150,
          490,   83,  150,  150,  150,  150,   51,   51,   51,   51,
          624,  799,  797,  627,  834,  375,  799,  799,  799,  270,
          158,   69,  197,  740,  760,  345,  788,  788,  801,  900,
          900,  788,  798,  788,  801,  914,  788,  788,  900,  900,
          835,  180,  550,  353,  524,  565,  900,  279,  788,  788,
          788,  788,  816,  571,  788,  214,  198,  788,  788,  816,
          811,  785,  145,  777,  900,  900,  900,  816,  500,  777,
          777,  777,  839,  845,  765,  784,  337,  297,  611,  169,
          822,  784,  784,  788,  538,  765,  784,  765,  784,  837,
          784,  784,  784,  765,  784,  798,  431,  784,  721,  607,
          163,  784,    6,  915,  916,  723,  917,  912,  918,  964,
          919,  923, 1054,  899,  930,  913,  924,  965,  906,  903,
          794,  693,  698,  827,  783,  896,  792,  792,  792,  894,
          792,  792,  792,  792,  792,  792,  792,  792,  693,  823,
          830,  787,  933,  702,  707, 1011,  819,  926, 1088,  932,
         1013,  925,  772,  711,  977,  934,  774, 1050,  935,  936,
          986, 1014,  846, 1017,  963,  796,  979, 1065,  836,  945,
         1055,  792,  915,  923,  735,  913,  924,  906,  903,  770,
          766,  762,  763,  761,  752,  747,  748,  782, 1018,  893,
          833,  880,  940,  895,  693,  886,  971, 1047,  990,  992,
         1053,  803,  791,  888, 1066,  946,  952,  953, 1056, 1019,
         1057,  838,  973,  775,  994,  820, 1067,  996,  997,  999,
         1000, 1058, 1068, 1059,  891, 1060,  849,  814,  966,  807,
         1069,    1,  806,  808,  818,  955,  484,  931, 1061, 1070,
         1071, 1001, 1002, 1006, 1072, 1073,  927,  852,  975,  815,
          976,  967,  855,  856,  525,  813, 1020,  800,  804,  812,
          577,  640, 1074, 1075, 1076,  928,  790,  786,  860,  864,
         1021,  809, 1022, 1077,  649,  867,  724, 1078, 1012,  744,
          754,  281,  654,  335,  756,  779, 1063,  829,  776,  778,
          954,  754,  793,  869, 1079,  870,  871,  872, 1007,  876,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          456,  456,  456,  456,  456,  456,  305,  305,  305,  305,
          305,  456,  456,  456,  456,  456,  456,  456,  305,  305,
            0,    0,  305,    0,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          473,  473,  289,  289,  473,  289,  473,  473,  473,  473,
          473,  473,  473,  473,  473,    0,  289,  289,  289,  289,
          289,  289,  289,  289,  473,  835,  473,  138,  138,  138,
          138,  473,  473,  473,  -88,  -88,  473,  238,  473,  473,
          138,  138,  473,  473,  473,  473,  473,  473,  473,  473,
          473,  473,  473,    0,    0,  384,  503,  473,  798,  798,
          798,  798,  473,  473,  473,  473,  503,  503,  473,  473,
          473,    0,    0,    0,    0,    0,    0,    0,    0,  384,
            0,    0,  384,    0,    0,  798,  798,  473,  238,  835,
          168,  473,    0,    0,    0,    0,  384,  798,  384,  443,
          788,  503,  503,  788,  443,  443,  150,   51,  168,  620,
          620,  620,  620,    0,    0,  624,  835,  835,  835,  835,
          835,  835,  835,  835,  835,  835,  835,  798,    0,  835,
            0,  798,  798,  798,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,  798,
            0,    0,  900,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,  914,    0,    0,    0,    0,    0,    0,
          798,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          792,  803,    0,  803,    0,  792,  792,  792,    0,    0,
            0,    0,  813,  809
    );

    protected array $actionDefault = array(
            3,32767,  102,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  100,32767,32767,32767,32767,  597,  597,
          597,  597,32767,32767,  254,  102,32767,32767,  470,  387,
          387,  387,32767,32767,  541,  541,  541,  541,  541,  541,
        32767,32767,32767,32767,32767,32767,  470,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  100,
        32767,32767,32767,   36,    7,    8,   10,   11,   49,   17,
          324,32767,32767,32767,32767,  102,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  590,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  474,  453,
          454,  456,  457,  386,  542,  596,  327,  593,  385,  145,
          339,  329,  242,  330,  258,  475,  259,  476,  479,  480,
          215,  287,  382,  149,  150,  417,  471,  419,  469,  473,
          418,  392,  398,  399,  400,  401,  402,  403,  404,  405,
          406,  407,  408,  409,  410,  390,  391,  472,  450,  449,
          448,32767,32767,  415,  416,32767,  420,32767,32767,32767,
        32767,32767,32767,32767,  102,32767,  389,  423,  421,  422,
          439,  440,  437,  438,  441,32767,32767,32767,  442,  443,
          444,  445,  316,32767,32767,  366,  364,  316,  111,32767,
        32767,  430,  431,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  535,  447,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  102,
        32767,  100,  537,  412,  414,  504,  425,  426,  424,  393,
        32767,  511,32767,  102,32767,  513,32767,32767,32767,32767,
        32767,32767,32767,  536,32767,  543,  543,32767,  497,  100,
          195,32767,32767,  512,32767,  195,  195,32767,32767,32767,
        32767,32767,32767,32767,32767,  604,  497,  110,  110,  110,
          110,  110,  110,  110,  110,  110,  110,  110,32767,  195,
          110,32767,32767,32767,  100,  195,  195,  195,  195,  195,
          195,  195,  195,  195,  195,  190,32767,  268,  270,  102,
          558,  195,32767,  516,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  509,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          497,  435,  138,32767,  138,  543,  427,  428,  429,  499,
          543,  543,  543,  312,  289,32767,32767,32767,32767,  514,
          514,  100,  100,  100,  100,  509,32767,32767,32767,32767,
          111,   99,   99,   99,   99,   99,  103,  101,32767,32767,
        32767,32767,  223,   99,32767,  101,  101,32767,32767,  223,
          225,  212,  101,  227,32767,  562,  563,  223,  101,  227,
          227,  227,  247,  247,  486,  318,  101,   99,  101,  101,
          197,  318,  318,32767,  101,  486,  318,  486,  318,  199,
          318,  318,  318,  486,  318,32767,  101,  318,  214,   99,
           99,  318,32767,32767,32767,  499,32767,32767,32767,32767,
        32767,32767,32767,  222,32767,32767,32767,32767,32767,32767,
        32767,32767,  530,32767,  547,  560,  433,  434,  436,  545,
          458,  459,  460,  461,  462,  463,  464,  466,  592,32767,
          503,32767,32767,32767,  338,32767,  602,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  603,32767,  543,32767,32767,32767,
        32767,  432,    9,   74,  492,   42,   43,   51,   57,  520,
          521,  522,  523,  517,  518,  524,  519,32767,32767,  525,
          568,32767,32767,  544,  595,32767,32767,32767,32767,32767,
        32767,  138,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  530,32767,  136,32767,32767,32767,32767,
        32767,32767,32767,32767,  526,32767,32767,32767,  543,32767,
        32767,32767,32767,  314,  311,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  543,32767,32767,32767,32767,32767,  291,32767,  308,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  286,32767,32767,  381,
          499,  294,  296,  297,32767,32767,32767,32767,  360,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          152,  152,    3,    3,  341,  152,  152,  152,  341,  341,
          152,  341,  341,  341,  152,  152,  152,  152,  152,  152,
          280,  185,  262,  265,  247,  247,  152,  352,  152
    );

    protected array $goto = array(
          196,  196, 1034, 1065,  697,  431,  661,  621,  658,  319,
          706,  425,  313,  314,  335,  576,  430,  336,  432,  638,
          654,  655,  852,  672,  673,  674,  853,  167,  167,  167,
          167,  221,  197,  193,  193,  177,  179,  216,  193,  193,
          193,  193,  193,  194,  194,  194,  194,  194,  194,  188,
          189,  190,  191,  192,  218,  216,  219,  536,  537,  421,
          538,  540,  541,  542,  543,  544,  545,  546,  547, 1136,
          168,  169,  170,  195,  171,  172,  173,  166,  174,  175,
          176,  178,  215,  217,  220,  238,  243,  244,  246,  257,
          258,  259,  260,  261,  262,  263,  264,  268,  269,  270,
          271,  281,  282,  316,  317,  318,  426,  427,  428,  581,
          222,  223,  224,  225,  226,  227,  228,  229,  230,  231,
          232,  233,  234,  235,  236,  180,  237,  181,  198,  199,
          200,  239,  188,  189,  190,  191,  192,  218, 1136,  201,
          182,  183,  184,  202,  198,  185,  240,  203,  201,  165,
          204,  205,  186,  206,  207,  208,  187,  209,  210,  211,
          212,  213,  214,  855,  466,  466,  278,  278,  278,  278,
          623,  623,  351,  466, 1269,  600, 1269, 1269, 1269, 1269,
         1269, 1269, 1269, 1269, 1269, 1287, 1287,  599, 1100, 1287,
          709, 1287, 1287, 1287, 1287, 1287, 1287, 1287, 1287, 1287,
          508,  700,  458, 1098,  975,  559,  552,  860,  419,  909,
          904,  905,  918,  861,  906,  858,  907,  908,  859,  848,
          827,  912,  354,  354,  354,  354,  396,  399,  560,  601,
          605, 1087, 1082, 1083, 1084,  341,  552,  559,  568,  569,
          344,  579,  602,  616,  617,  408,  409, 1232,  440,  479,
          670,   22,  671,  886,  412,  413,  414,  481,  684,  349,
         1237,  415, 1237, 1107, 1108,  347,  833, 1034, 1034, 1237,
          573,  848, 1034, 1327, 1034, 1034, 1040, 1039, 1034, 1034,
         1034, 1034, 1034, 1034, 1034, 1034, 1034, 1034, 1034, 1319,
         1319, 1319, 1319, 1237,  893,  851,  893,  893, 1237, 1237,
         1237, 1237, 1233, 1234, 1237, 1237, 1237,  833,  355,  833,
          843,  996,  252,  252, 1043, 1044, 1037, 1037,  355,  355,
          681,  952,  394,  926, 1029, 1045, 1046,  927, 1235, 1295,
         1296,  942,  355,  355,  942,  913,  355,  914, 1354,  250,
          250,  250,  250,  245,  253,  548,  548,  548,  548,  554,
          604, 1285, 1285,  355,  355, 1285,  571, 1285, 1285, 1285,
         1285, 1285, 1285, 1285, 1285, 1285,  539,  539,  342,  424,
          539,  611,  539,  539,  539,  539,  539,  539,  539,  539,
          539,  566,  476, 1312, 1313,  733,  637,  639,  325,  308,
          659,  848,  343,  342,  683,  687, 1010,  695,  704, 1006,
          660, 1298,  609,  624,  627,  628,  629,  630,  651,  652,
          653,  708, 1216,  944, 1314, 1315, 1217, 1220,  945, 1221,
         1337, 1337,  686,  352,  353,  868,  553,  563,  450,  450,
          450,  553, 1309,  563, 1309, 1133,  397,  462, 1337, 1058,
          880, 1309, 1185,  867,  500,    5,  501,    6,  469,  580,
          470,  471,  507,  554,  878, 1340, 1340, 1345, 1346,  433,
          438,  550,  666,  550,  433,  682, 1321, 1321, 1321, 1321,
          550,  337, 1041, 1041,  931, 1123,  873,  665, 1052, 1048,
         1049,  619,  845,  876,  324,  275,  324, 1015,  967,  410,
          705,  577,  614, 1305,  456,  872,  403,  664,  994,  969,
          969,  969,  969,  866,  870,  456,  963,  970,  881,  869,
         1070, 1074,  631,  633,  635, 1227, 1230,  958,  615,  978,
          450,  450,  450,  450,  450,  450,  450,  450,  450,  450,
          450,  999, 1018,  450,  971, 1073,  732,  477, 1228, 1307,
         1307, 1073,  736,  968,  551, 1008, 1003,  882,  694, 1075,
         1071,  829,  255,  255,  980,    0, 1118,    0, 1013, 1013,
          694,    0,    0,    0,  694, 1116,  885
    );

    protected array $gotoCheck = array(
           42,   42,   73,  127,   73,   66,   66,   56,   56,   66,
            9,   66,   66,   66,   66,   66,   66,   66,   66,   66,
           86,   86,   26,   86,   86,   86,   27,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   15,  149,  149,   23,   23,   23,   23,
          108,  108,   97,  149,  108,  130,  108,  108,  108,  108,
          108,  108,  108,  108,  108,  170,  170,    8,    8,  170,
            8,  170,  170,  170,  170,  170,  170,  170,  170,  170,
            8,    8,   83,    8,   49,   76,   76,   15,   43,   15,
           15,   15,   15,   15,   15,   15,   15,   15,   15,   22,
            6,   15,   24,   24,   24,   24,   59,   59,   59,   59,
           59,   15,   15,   15,   15,   76,   76,   76,   76,   76,
           76,   76,   76,   76,   76,   82,   82,   20,   83,   84,
           82,   76,   82,   45,   82,   82,   82,   84,   82,  179,
           73,   82,   73,  144,  144,   82,   12,   73,   73,   73,
          172,   22,   73,  181,   73,   73,  118,  118,   73,   73,
           73,   73,   73,   73,   73,   73,   73,   73,   73,    9,
            9,    9,    9,   73,   25,   25,   25,   25,   73,   73,
           73,   73,   20,   20,   73,   73,   73,   12,   14,   12,
           20,  103,    5,    5,  119,  119,   89,   89,   14,   14,
           89,   89,   62,   73,   89,   89,   89,   73,   20,   20,
           20,    9,   14,   14,    9,   65,   14,   65,   14,    5,
            5,    5,    5,    5,    5,  107,  107,  107,  107,   14,
          107,  171,  171,   14,   14,  171,  104,  171,  171,  171,
          171,  171,  171,  171,  171,  171,  173,  173,  168,   13,
          173,   13,  173,  173,  173,  173,  173,  173,  173,  173,
          173,   48,  176,  176,  176,   48,   48,   48,  169,  169,
           48,   22,  168,  168,   48,   48,   48,   48,   48,   48,
           64,   14,   81,   81,   81,   81,   81,   81,   81,   81,
           81,   81,   79,   79,  178,  178,   79,   79,   79,   79,
          182,  182,   14,   97,   97,   35,    9,    9,   23,   23,
           23,    9,  130,    9,  130,  150,    9,    9,  182,  114,
           35,  130,  151,   35,  155,   46,  155,   46,    9,    9,
            9,    9,  155,   14,    9,  182,  182,    9,    9,  117,
          113,   19,  120,   19,  117,  116,  130,  130,  130,  130,
           19,   29,  117,  117,   17,   17,   39,  117,  117,  117,
          117,   17,   18,    9,   24,   24,   24,   17,   93,   93,
           93,    2,    2,  130,   19,   17,   28,   17,   17,   19,
           19,   19,   19,   17,   37,   19,   19,   19,   16,   16,
           16,   16,   85,   85,   85,   17,   14,   92,   80,   16,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   50,  110,   23,   50,  130,   50,  157,  160,  130,
          130,  130,   99,   16,   50,   50,   50,   41,    7,  132,
          129,    7,    5,    5,   96,   -1,  147,   -1,  107,  107,
            7,   -1,   -1,   -1,    7,   16,   16
    );

    protected array $gotoBase = array(
            0,    0, -221,    0,    0,  311,  200,  541,  179,  -10,
            0,    0,  -30,   32,   11, -185,   56,    9,  173,  196,
         -146,    0,  -59,  163,  219,  291,   18,   22,  159,  175,
            0,    0,    0,    0,    0,   54,    0,  165,    0,  153,
            0,  106,   -1,  189,    0,  230, -291,    0, -330,  186,
          519,    0,    0,    0,    0,    0,  -33,    0,    0,  181,
            0,    0,  280,    0,  158,  321, -236,    0,    0,    0,
            0,    0,    0,   -5,    0,    0, -140,    0,    0,    4,
          174,   44, -246,  -76, -220,   33, -698,    0,    0,   37,
            0,    0,  188,  184,    0,    0,  111, -311,    0,  135,
            0,    0,    0,  276,  313,    0,    0,  317,  -71,    0,
          162,    0,    0,  183,  166,    0,  182,  187,   -3,   29,
          172,    0,    0,    0,    0,    0,    0,    1,    0,  176,
          167,    0,  107,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  -12,    0,    0,  112,    0,  130,
          190,  168,    0,    0,    0,  -51,    0,   97,    0,    0,
          169,    0,    0,    0,    0,    0,    0,    0,   71,   67,
          -56,  110,  241,  125,    0,    0,   82,    0,   42,  229,
            0,  242,  113,    0,    0
    );

    protected array $gotoDefault = array(
        -32768,  512,  740,    4,  741,  935,  816,  825,  597,  530,
          707,  348,  625,  422, 1303,  911, 1122,  578,  844, 1246,
         1254,  457,  847,  330,  730,  923,  894,  895,  400,  386,
          392,  398,  649,  626,  494,  879,  453,  871,  486,  874,
          452,  883,  164,  418,  510,  887,    3,  890,  557,  921,
          973,  387,  898,  388,  677,  900,  562,  902,  903,  395,
          401,  402, 1127,  570,  622,  915,  256,  564,  916,  385,
          917,  925,  390,  393,  688,  465,  505,  499,  411, 1102,
          565,  608,  646,  447,  473,  620,  632,  618,  480,  434,
          416,  329,  957,  965,  487,  463,  979,  350,  987,  738,
         1135,  640,  489,  995,  641, 1002, 1005,  531,  532,  478,
         1017,  272, 1020,  490,   19,  667, 1031, 1032,  668,  642,
         1054,  643,  669,  644, 1056,  472,  598, 1064,  454, 1072,
         1291,  455, 1076,  266, 1079,  277,  417,  435, 1085, 1086,
            9, 1092,  698,  699,   11,  276,  509, 1117,  689,  451,
         1134,  439, 1204, 1206,  558,  491, 1224, 1223,  680,  506,
         1229,  448, 1294,  449,  533,  474,  315,  534, 1338,  307,
          333,  312,  549,  294,  334,  535,  475, 1300, 1308,  331,
           31, 1328, 1339,  575,  613
    );

    protected array $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    7,    7,    7,
            7,    7,    7,    7,    7,    8,    8,    9,   10,   11,
           11,   11,   12,   12,   13,   13,   14,   15,   15,   16,
           16,   17,   17,   18,   18,   21,   21,   22,   23,   23,
           24,   24,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,   29,   29,   30,   30,   32,   34,   34,
           28,   36,   36,   33,   38,   38,   35,   35,   37,   37,
           39,   39,   31,   40,   40,   41,   43,   44,   44,   45,
           45,   46,   46,   48,   47,   47,   47,   47,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   25,   25,   50,   69,   69,   72,   72,   71,
           70,   70,   63,   75,   75,   76,   76,   77,   77,   78,
           78,   79,   79,   80,   80,   26,   26,   27,   27,   27,
           27,   27,   88,   88,   90,   90,   83,   83,   91,   91,
           92,   92,   92,   84,   84,   87,   87,   85,   85,   93,
           94,   94,   57,   57,   65,   65,   68,   68,   68,   67,
           95,   95,   96,   58,   58,   58,   58,   97,   97,   98,
           98,   99,   99,  100,  101,  101,  102,  102,  103,  103,
           55,   55,   51,   51,  105,   53,   53,  106,   52,   52,
           54,   54,   64,   64,   64,   64,   81,   81,  109,  109,
          111,  111,  112,  112,  112,  112,  110,  110,  110,  114,
          114,  114,  114,   89,   89,  117,  117,  117,  118,  118,
          115,  115,  119,  119,  121,  121,  122,  122,  116,  123,
          123,  120,  124,  124,  124,  124,  113,  113,   82,   82,
           82,   20,   20,   20,  126,  125,  125,  127,  127,  127,
          127,   60,  128,  128,  129,   61,  131,  131,  132,  132,
          133,  133,   86,  134,  134,  134,  134,  134,  134,  134,
          139,  139,  140,  140,  141,  141,  141,  141,  141,  142,
          143,  143,  138,  138,  135,  135,  137,  137,  145,  145,
          144,  144,  144,  144,  144,  144,  144,  136,  146,  146,
          148,  147,  147,   62,  104,  149,  149,   56,   56,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,  156,  150,  150,  155,  155,  158,  159,
          159,  160,  161,  162,  162,  162,  162,   19,   19,   73,
           73,   73,   73,  151,  151,  151,  151,  164,  164,  152,
          152,  154,  154,  154,  157,  157,  170,  170,  170,  170,
          170,  170,  170,  170,  170,  171,  171,  171,  108,  173,
          173,  173,  173,  153,  153,  153,  153,  153,  153,  153,
          153,   59,   59,  167,  167,  167,  167,  174,  174,  163,
          163,  163,  175,  175,  175,  175,  175,  175,   74,   74,
           66,   66,   66,   66,  130,  130,  130,  130,  178,  177,
          166,  166,  166,  166,  166,  166,  166,  165,  165,  165,
          176,  176,  176,  176,  107,  172,  180,  180,  179,  179,
          181,  181,  181,  181,  181,  181,  181,  181,  169,  169,
          169,  169,  168,  183,  182,  182,  182,  182,  182,  182,
          182,  182,  184,  184,  184,  184
    );

    protected array $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    0,
            1,    0,    1,    1,    2,    1,    3,    4,    1,    2,
            0,    1,    1,    1,    1,    4,    3,    5,    4,    3,
            4,    2,    3,    1,    1,    7,    6,    2,    3,    1,
            2,    3,    1,    2,    3,    1,    1,    3,    1,    3,
            1,    2,    2,    3,    1,    3,    2,    3,    1,    3,
            3,    2,    0,    1,    1,    1,    1,    1,    3,    7,
           10,    5,    7,    9,    5,    3,    3,    3,    3,    3,
            3,    1,    2,    5,    7,    9,    6,    5,    6,    3,
            2,    1,    1,    1,    1,    0,    2,    1,    3,    8,
            0,    4,    2,    1,    3,    0,    1,    0,    1,    0,
            1,    3,    1,    1,    1,    8,    9,    7,    8,    7,
            6,    8,    0,    2,    0,    2,    1,    2,    1,    2,
            1,    1,    1,    0,    2,    0,    2,    0,    2,    2,
            1,    3,    1,    4,    1,    4,    1,    1,    4,    2,
            1,    3,    3,    3,    4,    4,    5,    0,    2,    4,
            3,    1,    1,    7,    0,    2,    1,    3,    3,    4,
            1,    4,    0,    2,    5,    0,    2,    6,    0,    2,
            0,    3,    1,    2,    1,    1,    2,    0,    1,    3,
            0,    2,    1,    1,    1,    1,    6,    8,    6,    1,
            2,    1,    1,    1,    1,    1,    1,    1,    1,    3,
            3,    3,    1,    3,    3,    3,    3,    3,    1,    3,
            3,    1,    1,    2,    1,    1,    0,    1,    0,    2,
            2,    2,    4,    3,    1,    1,    3,    1,    2,    2,
            3,    2,    3,    1,    1,    2,    3,    1,    1,    3,
            2,    0,    1,    5,    5,    6,   10,    3,    5,    1,
            1,    3,    0,    2,    4,    5,    4,    4,    4,    3,
            1,    1,    1,    1,    1,    1,    0,    1,    1,    2,
            1,    1,    1,    1,    1,    1,    1,    2,    1,    3,
            1,    1,    3,    2,    2,    3,    1,    0,    1,    1,
            3,    3,    3,    4,    4,    1,    1,    2,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    2,    2,    2,    2,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    2,    2,    2,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    5,    4,
            3,    4,    4,    2,    2,    4,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    2,    1,    3,    2,
            1,    2,    4,    2,    2,    8,    9,    8,    9,    9,
           10,    9,   10,    8,    3,    2,    0,    4,    2,    1,
            3,    2,    1,    2,    2,    2,    4,    1,    1,    1,
            1,    1,    1,    1,    1,    3,    1,    1,    1,    0,
            3,    0,    1,    1,    0,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    3,    5,    3,    3,    4,
            1,    1,    3,    1,    1,    1,    1,    1,    3,    2,
            3,    0,    1,    1,    3,    1,    1,    1,    1,    1,
            3,    1,    1,    4,    4,    1,    4,    4,    0,    1,
            1,    1,    3,    3,    1,    4,    2,    2,    1,    3,
            1,    4,    4,    3,    3,    3,    3,    1,    3,    1,
            1,    3,    1,    1,    4,    1,    1,    1,    3,    1,
            1,    2,    1,    3,    4,    3,    2,    0,    2,    2,
            1,    2,    1,    1,    1,    4,    3,    3,    3,    3,
            6,    3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks(): void {
        $this->reduceCallbacks = [
            0 => null,
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; } $this->semValue = $this->semStack[$stackPos-(2-1)];;
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $nop = $this->maybeCreateZeroLengthNop($this->tokenPos);;
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => null,
            6 => null,
            7 => null,
            8 => null,
            9 => null,
            10 => null,
            11 => null,
            12 => null,
            13 => null,
            14 => null,
            15 => null,
            16 => null,
            17 => null,
            18 => null,
            19 => null,
            20 => null,
            21 => null,
            22 => null,
            23 => null,
            24 => null,
            25 => null,
            26 => null,
            27 => null,
            28 => null,
            29 => null,
            30 => null,
            31 => null,
            32 => null,
            33 => null,
            34 => null,
            35 => null,
            36 => null,
            37 => null,
            38 => null,
            39 => null,
            40 => null,
            41 => null,
            42 => null,
            43 => null,
            44 => null,
            45 => null,
            46 => null,
            47 => null,
            48 => null,
            49 => null,
            50 => null,
            51 => null,
            52 => null,
            53 => null,
            54 => null,
            55 => null,
            56 => null,
            57 => null,
            58 => null,
            59 => null,
            60 => null,
            61 => null,
            62 => null,
            63 => null,
            64 => null,
            65 => null,
            66 => null,
            67 => null,
            68 => null,
            69 => null,
            70 => null,
            71 => null,
            72 => null,
            73 => null,
            74 => null,
            75 => null,
            76 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; if ($this->semValue === "<?=") $this->emitError(new Error('Cannot use "<?=" as an identifier', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])));
            },
            77 => null,
            78 => null,
            79 => null,
            80 => null,
            81 => null,
            82 => null,
            83 => null,
            84 => null,
            85 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            88 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            89 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            90 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            92 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            93 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            94 => null,
            95 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            96 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            97 => function ($stackPos) {
                 /* nothing */
            },
            98 => function ($stackPos) {
                 /* nothing */
            },
            99 => function ($stackPos) {
                 /* nothing */
            },
            100 => function ($stackPos) {
                 $this->emitError(new Error('A trailing comma is not allowed here', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])));
            },
            101 => null,
            102 => null,
            103 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(1-1)], [], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            104 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            105 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            106 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            107 => function ($stackPos) {
                 $this->semValue = new Node\AttributeGroup($this->semStack[$stackPos-(4-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            108 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            109 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            110 => function ($stackPos) {
                 $this->semValue = [];
            },
            111 => null,
            112 => null,
            113 => null,
            114 => null,
            115 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->handleHaltCompiler(), $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            118 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            119 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            120 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            121 => null,
            122 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            123 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            124 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            125 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->semStack[$stackPos-(7-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            126 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            127 => null,
            128 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            129 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            130 => null,
            131 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            132 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            133 => null,
            134 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            135 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            136 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            137 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            138 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            139 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            140 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            141 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            142 => null,
            143 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            144 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            145 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            146 => null,
            147 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            148 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            149 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)],  $this->tokenEndStack[$stackPos-(3-1)])), $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            150 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)],  $this->tokenEndStack[$stackPos-(3-1)])), $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            151 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; } $this->semValue = $this->semStack[$stackPos-(2-1)];;
            },
            152 => function ($stackPos) {
                 $this->semValue = array();
            },
            153 => function ($stackPos) {
                 $nop = $this->maybeCreateZeroLengthNop($this->tokenPos);;
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            154 => null,
            155 => null,
            156 => null,
            157 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            158 => function ($stackPos) {
                 $this->semValue = new Stmt\Block($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            159 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(7-3)], ['stmts' => $this->semStack[$stackPos-(7-5)], 'elseifs' => $this->semStack[$stackPos-(7-6)], 'else' => $this->semStack[$stackPos-(7-7)]], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(10-3)], ['stmts' => $this->semStack[$stackPos-(10-6)], 'elseifs' => $this->semStack[$stackPos-(10-7)], 'else' => $this->semStack[$stackPos-(10-8)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            },
            161 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            162 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(7-5)], $this->semStack[$stackPos-(7-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            163 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            164 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            165 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            167 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            169 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            170 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            171 => function ($stackPos) {

        $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
        $this->semValue->setAttribute('hasLeadingNewline', $this->inlineHtmlHasLeadingNewline($stackPos-(1-1)));

            },
            172 => function ($stackPos) {
                 $this->semValue = new Stmt\Expression($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            173 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            174 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            175 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            176 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(6-3)], new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(6-4)],  $this->tokenEndStack[$stackPos-(6-4)])), ['stmts' => $this->semStack[$stackPos-(6-6)]], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            177 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            178 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos])); $this->checkTryCatch($this->semValue);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            181 => function ($stackPos) {
                 $this->semValue = null; /* means: no statement */
            },
            182 => null,
            183 => function ($stackPos) {
                 $this->semValue = $this->maybeCreateNop($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]);
            },
            184 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $this->semValue = $this->semStack[$stackPos-(1-1)]->stmts; } else if ($this->semStack[$stackPos-(1-1)] === null) { $this->semValue = []; } else { $this->semValue = [$this->semStack[$stackPos-(1-1)]]; };
            },
            185 => function ($stackPos) {
                 $this->semValue = array();
            },
            186 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            187 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            188 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            189 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            190 => function ($stackPos) {
                 $this->semValue = null;
            },
            191 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            192 => null,
            193 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            194 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            195 => function ($stackPos) {
                 $this->semValue = false;
            },
            196 => function ($stackPos) {
                 $this->semValue = true;
            },
            197 => function ($stackPos) {
                 $this->semValue = false;
            },
            198 => function ($stackPos) {
                 $this->semValue = true;
            },
            199 => function ($stackPos) {
                 $this->semValue = false;
            },
            200 => function ($stackPos) {
                 $this->semValue = true;
            },
            201 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            202 => function ($stackPos) {
                 $this->semValue = [];
            },
            203 => null,
            204 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            205 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(8-3)], ['byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-5)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            206 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(9-4)], ['byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            207 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(7-2)], ['type' => $this->semStack[$stackPos-(7-1)], 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            $this->checkClass($this->semValue, $stackPos-(7-2));
            },
            208 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(8-3)], ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            $this->checkClass($this->semValue, $stackPos-(8-3));
            },
            209 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(7-3)], ['extends' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => $this->semStack[$stackPos-(7-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            $this->checkInterface($this->semValue, $stackPos-(7-3));
            },
            210 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(6-3)], ['stmts' => $this->semStack[$stackPos-(6-5)], 'attrGroups' => $this->semStack[$stackPos-(6-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            211 => function ($stackPos) {
                 $this->semValue = new Stmt\Enum_($this->semStack[$stackPos-(8-3)], ['scalarType' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            $this->checkEnum($this->semValue, $stackPos-(8-3));
            },
            212 => function ($stackPos) {
                 $this->semValue = null;
            },
            213 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            214 => function ($stackPos) {
                 $this->semValue = null;
            },
            215 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            216 => function ($stackPos) {
                 $this->semValue = 0;
            },
            217 => null,
            218 => null,
            219 => function ($stackPos) {
                 $this->checkClassModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            220 => function ($stackPos) {
                 $this->semValue = Modifiers::ABSTRACT;
            },
            221 => function ($stackPos) {
                 $this->semValue = Modifiers::FINAL;
            },
            222 => function ($stackPos) {
                 $this->semValue = Modifiers::READONLY;
            },
            223 => function ($stackPos) {
                 $this->semValue = null;
            },
            224 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            225 => function ($stackPos) {
                 $this->semValue = array();
            },
            226 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            227 => function ($stackPos) {
                 $this->semValue = array();
            },
            228 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            229 => null,
            230 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            231 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            232 => null,
            233 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            234 => null,
            235 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            236 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $this->semValue = $this->semStack[$stackPos-(1-1)]->stmts; } else if ($this->semStack[$stackPos-(1-1)] === null) { $this->semValue = []; } else { $this->semValue = [$this->semStack[$stackPos-(1-1)]]; };
            },
            237 => function ($stackPos) {
                 $this->semValue = null;
            },
            238 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            239 => null,
            240 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            241 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            242 => function ($stackPos) {
                 $this->semValue = new Node\DeclareItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            243 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            244 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            245 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            246 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            247 => function ($stackPos) {
                 $this->semValue = array();
            },
            248 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            249 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            250 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            251 => null,
            252 => null,
            253 => function ($stackPos) {
                 $this->semValue = new Expr\Match_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            254 => function ($stackPos) {
                 $this->semValue = [];
            },
            255 => null,
            256 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            257 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            258 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            259 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm(null, $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            260 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            261 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            262 => function ($stackPos) {
                 $this->semValue = array();
            },
            263 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            264 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            265 => function ($stackPos) {
                 $this->semValue = array();
            },
            266 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            267 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-6)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos])); $this->fixupAlternativeElse($this->semValue);
            },
            268 => function ($stackPos) {
                 $this->semValue = null;
            },
            269 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            270 => function ($stackPos) {
                 $this->semValue = null;
            },
            271 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->fixupAlternativeElse($this->semValue);
            },
            272 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            273 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            274 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            275 => function ($stackPos) {
                 $this->semValue = array($this->fixupArrayDestructuring($this->semStack[$stackPos-(1-1)]), false);
            },
            276 => null,
            277 => function ($stackPos) {
                 $this->semValue = array();
            },
            278 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            279 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            280 => function ($stackPos) {
                 $this->semValue = 0;
            },
            281 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            282 => function ($stackPos) {
                 $this->semValue = Modifiers::PUBLIC;
            },
            283 => function ($stackPos) {
                 $this->semValue = Modifiers::PROTECTED;
            },
            284 => function ($stackPos) {
                 $this->semValue = Modifiers::PRIVATE;
            },
            285 => function ($stackPos) {
                 $this->semValue = Modifiers::READONLY;
            },
            286 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-6)], null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            $this->checkParam($this->semValue);
            },
            287 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-8)], $this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(8-2)], $this->semStack[$stackPos-(8-1)]);
            $this->checkParam($this->semValue);
            },
            288 => function ($stackPos) {
                 $this->semValue = new Node\Param(new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos])), null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            },
            289 => null,
            290 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            291 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            292 => null,
            293 => null,
            294 => function ($stackPos) {
                 $this->semValue = new Node\Name('static', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            295 => function ($stackPos) {
                 $this->semValue = $this->handleBuiltinTypes($this->semStack[$stackPos-(1-1)]);
            },
            296 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            297 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            298 => null,
            299 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            300 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            301 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            302 => null,
            303 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            304 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            305 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            306 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            307 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            308 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            309 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            310 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            311 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            312 => null,
            313 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            314 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            315 => null,
            316 => function ($stackPos) {
                 $this->semValue = null;
            },
            317 => null,
            318 => function ($stackPos) {
                 $this->semValue = null;
            },
            319 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            320 => function ($stackPos) {
                 $this->semValue = null;
            },
            321 => function ($stackPos) {
                 $this->semValue = array();
            },
            322 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            323 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-2)]);
            },
            324 => function ($stackPos) {
                 $this->semValue = new Node\VariadicPlaceholder($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            325 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            326 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            327 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            328 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            329 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            330 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(3-3)], false, false, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(3-1)]);
            },
            331 => null,
            332 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            333 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            334 => null,
            335 => null,
            336 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            337 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            338 => function ($stackPos) {
                 $this->semValue = new Node\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            339 => function ($stackPos) {
                 $this->semValue = new Node\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            340 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; } else { $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            341 => function ($stackPos) {
                 $this->semValue = array();
            },
            342 => function ($stackPos) {
                 $nop = $this->maybeCreateZeroLengthNop($this->tokenPos);;
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            343 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-1)]);
            $this->checkProperty($this->semValue, $stackPos-(5-2));
            },
            344 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(5-1)]);
            $this->checkClassConst($this->semValue, $stackPos-(5-2));
            },
            345 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-4)]);
            $this->checkClassConst($this->semValue, $stackPos-(6-2));
            },
            346 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(10-5)], ['type' => $this->semStack[$stackPos-(10-2)], 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-7)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            $this->checkClassMethod($this->semValue, $stackPos-(10-2));
            },
            347 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            348 => function ($stackPos) {
                 $this->semValue = new Stmt\EnumCase($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            349 => function ($stackPos) {
                 $this->semValue = null; /* will be skipped */
            },
            350 => function ($stackPos) {
                 $this->semValue = array();
            },
            351 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            352 => function ($stackPos) {
                 $this->semValue = array();
            },
            353 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            354 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            355 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            356 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            357 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            358 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            359 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            360 => null,
            361 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            362 => function ($stackPos) {
                 $this->semValue = null;
            },
            363 => null,
            364 => null,
            365 => function ($stackPos) {
                 $this->semValue = 0;
            },
            366 => function ($stackPos) {
                 $this->semValue = 0;
            },
            367 => null,
            368 => null,
            369 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            370 => function ($stackPos) {
                 $this->semValue = Modifiers::PUBLIC;
            },
            371 => function ($stackPos) {
                 $this->semValue = Modifiers::PROTECTED;
            },
            372 => function ($stackPos) {
                 $this->semValue = Modifiers::PRIVATE;
            },
            373 => function ($stackPos) {
                 $this->semValue = Modifiers::STATIC;
            },
            374 => function ($stackPos) {
                 $this->semValue = Modifiers::ABSTRACT;
            },
            375 => function ($stackPos) {
                 $this->semValue = Modifiers::FINAL;
            },
            376 => function ($stackPos) {
                 $this->semValue = Modifiers::READONLY;
            },
            377 => null,
            378 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            379 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            380 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            381 => function ($stackPos) {
                 $this->semValue = new Node\PropertyItem($this->semStack[$stackPos-(1-1)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            382 => function ($stackPos) {
                 $this->semValue = new Node\PropertyItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            383 => null,
            384 => null,
            385 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            386 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            387 => function ($stackPos) {
                 $this->semValue = array();
            },
            388 => null,
            389 => null,
            390 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            391 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->fixupArrayDestructuring($this->semStack[$stackPos-(3-1)]), $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            392 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            393 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            394 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            if (!$this->phpVersion->allowsAssignNewByReference()) {
                $this->emitError(new Error('Cannot assign new by reference', $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos])));
            }

            },
            395 => null,
            396 => null,
            397 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            399 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            400 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            401 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            402 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            403 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            405 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            410 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            413 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            414 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            415 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            416 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            417 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            418 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            419 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            420 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            421 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            422 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            423 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            424 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            429 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            430 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            431 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            432 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            433 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            434 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            435 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            436 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            437 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            438 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            439 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            440 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            441 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            442 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            443 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            444 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            445 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            446 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            447 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            448 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            449 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            450 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            451 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            455 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            456 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            458 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            459 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]);
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            462 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            464 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            465 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]);
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            466 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            467 => null,
            468 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            469 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            470 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            471 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            472 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            473 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            474 => function ($stackPos) {
                 $this->semValue = new Expr\Throw_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'returnType' => $this->semStack[$stackPos-(8-6)], 'expr' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'uses' => $this->semStack[$stackPos-(8-6)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            479 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            480 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-8)], 'expr' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            },
            481 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            482 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'uses' => $this->semStack[$stackPos-(10-8)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            },
            483 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos])), $this->semStack[$stackPos-(8-3)]);
            $this->checkClass($this->semValue[0], -1);
            },
            484 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            485 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            486 => function ($stackPos) {
                 $this->semValue = array();
            },
            487 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            488 => null,
            489 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            490 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            491 => function ($stackPos) {
                 $this->semValue = new Node\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            492 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            493 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            494 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            495 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            496 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            497 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            498 => null,
            499 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            500 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            501 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            502 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            503 => null,
            504 => null,
            505 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            506 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            507 => null,
            508 => null,
            509 => function ($stackPos) {
                 $this->semValue = null;
            },
            510 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            511 => function ($stackPos) {
                 $this->semValue = array();
            },
            512 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]); foreach ($this->semValue as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $this->phpVersion->supportsUnicodeEscapes()); } };
            },
            513 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $this->phpVersion->supportsUnicodeEscapes()); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            514 => function ($stackPos) {
                 $this->semValue = array();
            },
            515 => null,
            516 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            517 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            518 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            519 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            520 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            521 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            522 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            523 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            524 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            525 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            526 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            527 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(3-3)],  $this->tokenEndStack[$stackPos-(3-3)])), $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            528 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            529 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            $this->createdArrays->attach($this->semValue);
            },
            530 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->createdArrays->attach($this->semValue);
            },
            531 => function ($stackPos) {
                 $this->semValue = Scalar\String_::fromString($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]), $this->phpVersion->supportsUnicodeEscapes());
            },
            532 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]); $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', $this->phpVersion->supportsUnicodeEscapes()); } }; $this->semValue = new Scalar\InterpolatedString($this->semStack[$stackPos-(3-2)], $attrs);
            },
            533 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]), $this->phpVersion->allowsInvalidOctals());
            },
            534 => function ($stackPos) {
                 $this->semValue = Scalar\Float_::fromString($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            535 => null,
            536 => null,
            537 => null,
            538 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]), $this->getAttributes($this->tokenStartStack[$stackPos-(3-3)],  $this->tokenEndStack[$stackPos-(3-3)]), true);
            },
            539 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]), $this->getAttributes($this->tokenStartStack[$stackPos-(2-2)],  $this->tokenEndStack[$stackPos-(2-2)]), true);
            },
            540 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]), $this->getAttributes($this->tokenStartStack[$stackPos-(3-3)],  $this->tokenEndStack[$stackPos-(3-3)]), true);
            },
            541 => function ($stackPos) {
                 $this->semValue = null;
            },
            542 => null,
            543 => null,
            544 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            545 => null,
            546 => null,
            547 => null,
            548 => null,
            549 => null,
            550 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            551 => null,
            552 => null,
            553 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            554 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            555 => null,
            556 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            557 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafeMethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            558 => function ($stackPos) {
                 $this->semValue = null;
            },
            559 => null,
            560 => null,
            561 => null,
            562 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            563 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            564 => null,
            565 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            566 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            567 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos])), $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            568 => function ($stackPos) {
                 $var = $this->semStack[$stackPos-(1-1)]->name; $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])) : $var;
            },
            569 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            570 => null,
            571 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            572 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            573 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            574 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            575 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            576 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            577 => null,
            578 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            579 => null,
            580 => null,
            581 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            582 => null,
            583 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            584 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos])); $this->semValue->setAttribute('kind', Expr\List_::KIND_LIST);
            $this->postprocessList($this->semValue);
            },
            585 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $end = count($this->semValue)-1; if ($this->semValue[$end]->value instanceof Expr\Error) array_pop($this->semValue);
            },
            586 => null,
            587 => function ($stackPos) {
                 /* do nothing -- prevent default action of $$=$this->semStack[$1]. See $551. */
            },
            588 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            589 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            590 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            591 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            592 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            593 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            594 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            595 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            596 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]), true);
            },
            597 => function ($stackPos) {
                 /* Create an Error node now to remember the position. We'll later either report an error,
             or convert this into a null element, depending on whether this is a creation or destructuring context. */
          $attrs = $this->createEmptyElemAttributes($this->tokenPos);
          $this->semValue = new Node\ArrayItem(new Expr\Error($attrs), null, false, $attrs);
            },
            598 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            599 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            600 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            601 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            602 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]); $attrs['rawValue'] = $this->semStack[$stackPos-(1-1)]; $this->semValue = new Node\InterpolatedStringPart($this->semStack[$stackPos-(1-1)], $attrs);
            },
            603 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            604 => null,
            605 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            606 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            607 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            608 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            609 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            610 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            611 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            612 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            613 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            614 => function ($stackPos) {
                 $this->semValue = $this->parseNumString('-' . $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            615 => null,
        ];
    }
}
