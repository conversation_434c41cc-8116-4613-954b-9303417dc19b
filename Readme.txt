=== Import Export Suite for CSV and XML Datafeed ===
Contributors: smackcoders, smacksupport
Donate link: https://www.smackcoders.com/contact-us.html
Requires at least: 5.0
Tested up to: 6.7
Stable tag: 7.18
Requires PHP: 7.4
Author URI: https://www.smackcoders.com/wp-ultimate-csv-importer-pro.html
Tags: import export, wordpress csv import, xml, csv, all import, import all, datafeed, importer, import, xml import, migrate, import csv to wordpress, import xml to wordpress, advanced xml import, advanced csv import, bulk csv import, bulk xml import, bulk data import, xml to custom post type, csv to custom post type, woocommerce csv import, woocommerce xml import, csv import, import csv, wordpress xml import, import xml, csv importer
License: GPLv2 or later

Simplify your WP import export management today with our Ultimate CSV Importer plugin. Launch your website on time with accurate data handling!

== Description ==

**Tired of wasting time on manual uploads?**

Our WP Import Export plugin features and add-ons are your one-stop solution for all your WordPress data migration needs. Import and export everything from posts and pages to products and custom fields.

Expand the capabilities of your WP Import-Export plugin with our powerful add-ons. Easily import, export, and manage users, products, and custom data types.

**Explore our [PRO DEMO](https://demo.smackcoders.com/wordpress/wp-admin/?utm_source=csv_importer_free&utm_medium=wporgreadme&utm_campaign=csv_importer_pro_demo). Ready for more? Try our [Private Trial](https://trial.smackcoders.com/index.html?utm_source=csv-importer-free&utm_medium=wporgreadme&utm_campaign=csv_importer_pro_trial).**

[youtube https://www.youtube.com/watch?v=dy3pdwoujxQ]

**Key Benefits:**

* **Accelerate Workflow:** Easily handle large datasets in minutes, saving valuable time and simplifying website updates. Whether launching a new site or updating content, the bulk import/export features boost productivity and reduce manual work.  

* **Seamless Compatibility:** Enjoy smooth integration with tools like WooCommerce, Yoast SEO, WPML, and Advanced Custom Fields. This ensures your core content, taxonomies, multilingual data, and SEO settings migrate without any hassle.  

* **Single Post Import-Export:** Instant easy export or import of individual posts, pages, and custom post content as CSV directly from the edit or create view, eliminating the need to process a bulk dataset.

* **Error-Free Migrations:** Minimize mistakes with the plugin's intuitive drag-and-drop interface and detailed logs. Support for CSV and XML formats ensures accurate mapping and preserves data integrity during every import or export.  

* **Automated Updates:** Automate content management by scheduling imports and exports (available in the Premium version). Set specific times and intervals to effortlessly keep your website up-to-date—ideal for inventory updates, blog content, or regular data refreshes.  

= User-Friendly Interface =  

* **Seamless Imports:** Upload your **CSV** or **XML** files effortlessly. The intuitive interface simplifies even the most extensive imports, making it accessible to all users.  

* **Smart Field Mapping:** Quickly align data fields with **WordPress** fields for efficient and precise data transfers.  

* **Real-Time Progress Tracking:** Monitor imports live to identify and resolve issues instantly.  

* **Comprehensive Logs:** Access detailed import logs for transparency and troubleshooting.  

* **Pre-Import Validation:** Ensure error-free imports by validating your data before uploading.  

= Flexible File Uploads =

* **Multiple Upload Options:** Import files directly from your device, a URL, or **FTP/SFTP** sources.  

* **Broad Format Support:** Easily handle file types like **CSV, XML**, and more, ensuring compatibility with various platforms.  

* **Customizable Imports:** Leverage custom functions to tailor the import process to your specific requirements.  

= Automatic Media Handling = 

* **Hassle-Free Image Imports:** Automatically add and link images to your posts or products during the import process.  

* **Boost SEO:** Enhance image visibility with auto-generated alt text and titles for search engines.  

* **Efficient Media Management:** Simplify handling of large media libraries without manual uploads.  

* **Resolve Media Import Failures:** Detect and recover failed image imports for a complete and accurate media library.  

= Popular Plugins Integration =
* Advanced Custom Fields (ACF): Basic, Choices, and jQuery fields.  
* MetaBox Plugin ~ Basic & Limited Advanced fields.  
* Pods ~ CPT and All Fields.  
* JetEngine ~ Basic fields (CPTs & CCTs).  
* Toolset Types ~ Basic fields.  
* Custom Field Suite plugin.  
* CPT UI.  
* WooCommerce ~ Products, Coupons, Orders, Reviews, and Refunds. WooCommerce products are imported with categories, tags, gallery images, and attributes.  
* WooCommerce Product Bundles Plugin.  
* WooCommerce Billing & Shipping Information.  
* WordPress SEO Plugins: AIOSEO (free & pro), RankMath SEO FREE, SEOPress (free & pro), Yoast SEO.  
* WPML (posts & pages).  
* Polylang Free.  
* Polylang for WooCommerce Plugin.  
* WP Job Manager Plugin.  
* TotalPress Custom post types, Custom Fields & more ~ CPTs, Custom Fields.  
* WP Customer Reviews.  
* LearnPress plugin ~ Courses, Lessons, Quizzes, Orders, and Questions.  
* LifterLMS plugin ~ Courses, Coupons, Reviews, Lessons.  
* MasterStudy LMS plugin ~ Courses, Lessons, Quizzes, Questions, and Orders.  
* BuddyPress Import.  
* bbPress ~ Topic, Reply, and Forum.  
* Post Expirator Plugin.  
* Featured Image from URL (FIFU).  
* YITH Barcodes.  
* YITH Cost of Goods.  
* Membership plugins: WP-Members, Members.  
* WPComplete.  
* Exmage image.  
* Elementor Templates.  
* ChatGPT OpenAI.  
* Business Directory Plugin.  
* Advanced Classifieds & Directory Pro.  
* GeoDirectory.  
* PPOM (Personalized Product Option Manager) fields.  

= Top WordPress CSV Importer Features = 

Easily manage bulk imports and migrate thousands of records in minutes with the plugin’s powerful capabilities. Create and prepare CSV files using tools like Google Sheets or Excel, and enjoy seamless importing with the intuitive field-mapping feature. The plugin supports **Advanced Custom Fields (ACF)** in this free version, however, with limited field compatibility, including **Basic fields**, **Choices**, and **JQuery fields**, making it ideal for handling custom data.

= WordPress XML Import Features =
Simplify complex migrations with full support for XML file imports. This feature ensures the integrity of intricate data structures, enabling direct XML uploads without any additional steps. The plugin supports importing **custom post types** such as products, events, or portfolios while preserving categories, tags, and taxonomies for a flawless transition.  

= Our WordPress Import Export Plugin Addons = 

Enhance your website management experience with powerful plugin add-ons designed to make importing, exporting, and maintaining WordPress content effortless.

= Ultimate WordPress Exporter Addon =
Efficiently export WordPress content with our [WordPress All Export for CSV and XML](https://wordpress.org/plugins/wp-ultimate-exporter) addon. This tool turns content management into a two-way process, streamlining backups, migrations, and sharing.

* **Quick Backups:** Export posts, pages, custom post types, and comments in one click.  
* **Easy Migrations:** Transfer your website to a new host or domain with ease.  
* **Filtered Exports:** Select specific content using built-in filters.  
* **Export with Media:** Include images for a complete content package.  

= WordPress User Import & Export Addon =

Effortlessly manage user accounts with the [WordPress Users & Members Import from CSV](https://wordpress.org/plugins/import-users) addon and its export counterpart.  

* **Bulk Import Users:** Quickly add large numbers of users with detailed CSV files.  
* **User Migration:** Import users from other platforms or databases with format flexibility.  
* **Export for Backups:** Generate comprehensive backups of user data, including roles and profiles.  
* **Secure Password Management:** Safely import encrypted user passwords.  

= WooCommerce Product Import & Export Addon =
Simplify product management for your WooCommerce store using the [WooCommerce Products & Bundle Import](https://wordpress.org/plugins/import-woocommerce) addon.  

* **Bulk Product Uploads:** Quickly import large inventories via CSV or XML.  
* **Media Integration:** Import products with images for fully detailed listings.  
* **Attribute Management:** Automate the addition of details like size, color, and material.  
* **Category Organization:** Maintain proper classification with category-specific imports.  
* **Flexible Formats:** Handle CSV, Excel, and XML files for data from multiple sources.  

= WordPress Multilingual Content Import & Export =

Expand your global reach with seamless multilingual content management. The plugin integrates with WPML and Polylang for effortless importing and exporting of multilingual content.

* **Multilingual Migration:** Migrate all language versions in a single operation.  
* **Centralized Management:** Organize multilingual content efficiently in one location.  
* **Time-Saving Automation:** Eliminate manual translation uploads, focusing on your global strategy.

= Single Post CSV Import Export =

Experience the convenience of the instant one-click CSV export and import feature for individual posts, pages & custom post records. Easily export or import your desired content directly from the create or edit view.

* Back up individual pages and posts.  
* Migrate content between test and live sites.  
* Supports core fields, metadata, and taxonomy term fields.  

Perfect for backups or migrations in just a single click.

= Premium Features =

Upgrade to our Pro version to unlock advanced features like:

* **ACF & Other Custom Fields Plugin Import:** Supports ACF, JetEngine, CMB2, and Toolset fields.  
* **Multilingual Support:** Compatible with WPML, qTranslate X, and Polylang.  
* **WooCommerce Data:** Import products, variations, orders, and more with full control.  
* **Yoast SEO PRO Integration:** Import SEO data like meta titles and descriptions.  
* **Scheduled Imports:** Automate imports by scheduling them in the background.  
* **Advanced Export Options:** Filter and export your WordPress content into different formats.  

[Explore WP Ultimate CSV Importer Pro Features](https://www.smackcoders.com/wp-ultimate-csv-importer-pro.html?utm_source=web&utm_campaign=readme&utm_medium=wporg).

= Ready to Get Started? =

* [Try Our PRO DEMO](https://demo.smackcoders.com/wordpress/wp-admin/?utm_source=csv_importer_free&utm_medium=wporgreadme&utm_campaign=csv_importer_pro_demo)
* [Play with Our Private Trial](https://trial.smackcoders.com/index.html?utm_source=csv-importer-free&utm_medium=wporgreadme&utm_campaign=csv_importer_pro_trial)
* [Check Out Our Documentation](https://www.smackcoders.com/documentation/ultimate-csv-importer-pro/how-to-import-csv?utm_source=web&utm_campaign=readme&utm_medium=wporg)

= Watch Our YouTube Tutorials =
[youtube https://www.youtube.com/watch?v=M78qYD89B8c&list=PL2k3Ck1bFtbTT-5Tz2PLkoAUJ1fRdwUSj&index=2]


== Screenshots ==
1. Plugin Dashboard Overview
2. Media Upload Methods
3. Local Image Upload View
4. Zip-Uploaded Images
5. Media Import Complete
6. Log Manager for Imports
7. CSV/XML File Upload Screen
8. Drag and Drop Mapping Tool
9. Field Mapping View
10. Real-Time Import Log
11. PHP.ini Settings for Imports


== FREQUENTLY ASKED QUESTIONS ==

= How to Validate a CSV File for WordPress Import =
CSV files must be error-free to ensure a successful import. Use a free tool like **CSV Lint** to identify and correct issues such as missing or misplaced characters before proceeding.

= How to Import XML into WordPress =
To import **XML files**:  
1. Upload your XML file via the plugin interface.  
2. The plugin maps fields to WordPress content types, including **posts**, **pages**, and **custom fields**.  

This process ensures data integrity and retains your existing site structure.

= Can I Get Sample Files for Testing the Import? =
Yes! Download free sample CSV files specifically designed for testing the import functionality with the Ultimate CSV Importer plugin: [Link to Sample CSV Files](https://www.smackcoders.com/blog/wordpress-ultimate-csv-importer-csv-sample-files-and-updates.html?utmsource=web&utmcampaign=readme&utmmedium=wporg)

= Why Are Posts Not Showing After Import? =
This may happen due to:  
1. **Missing Mandatory Fields:** Ensure all required fields have valid values.  
2. **Debugging Interference:** Set `WP_DEBUG` to `false` in the `wp-config.php` file before importing.

= How to Import Posts with Images =
Use the **Automatic Media Handling** feature to:  
* Import and link images from CSV or XML files directly to posts or products.  
* Automatically add images to your WordPress media library.  
* Enhance image SEO by including alt text and titles.  

This feature simplifies managing large image imports.

= Why Do Images Fail to Import into the Media Library? =
Common reasons include:  
1. **Unsupported Formats:** Ensure the image format is compatible with WordPress.  
2. **Permission Issues:** Verify that the image URL is publicly accessible.  
3. **Invalid URLs:** Ensure URLs are correct and secured (use HTTPS).

= How to Import Images from External URLs =
Enable the **"Download Post-Content External Images to Media"** option in the **Manage Media Uploads** section. This allows you to import images from sources like **Google Images**, **Pexels**, and **Dropbox**.  
[See the documentation for more details](https://www.smackcoders.com/documentation/wp-ultimate-csv-importer-pro/import-images-into-wordpress?utmsource=web&utmcampaign=readme&utmmedium=wporg).

= How to Import Users into WordPress =
The User Import Export feature enables:  
* Bulk importing users with metadata from **CSV** or **XML** files.  
* Mapping fields like names, emails, roles, and custom metadata.  
* Securely importing passwords and roles for seamless user onboarding.

= How to Import and Export Custom Post Types =
The plugin supports importing and exporting **custom post types** (e.g., products, events, portfolios). You can map fields for custom post types and their associated taxonomies using **CSV** or **XML** files.

= Can I Export Data Using the Plugin? =
Yes, with the [WP Ultimate Exporter addon](https://wordpress.org/plugins/wp-ultimate-exporter/), you can export posts, users, WooCommerce products, custom fields, and more in **CSV** and **XML** formats.

= In Which Formats Can I Export Data? =
Data can be exported in **CSV** or **XML** formats, compatible with tools like **Google Sheets** and **Excel**.

= How to Export Posts from WordPress with Images =
The **Automatic Media Handling** feature exports posts with their images intact. Images are linked and included alongside their respective posts for a complete and efficient export.

= Is There a Limit to the Number of Rows I Can Import in One CSV File? =
No, you can import an unlimited number of rows with this plugin. It can handle even the largest datasets effortlessly. However, importing a very large file may depend on your server's PHP settings (like maximum file size or execution time). You can check with your hosting provider for more details.

= Can I Update Existing Content Using This Plugin? =
Updating existing content features are available only in our Pro version. For advanced features like scheduling data imports and exports, you can upgrade to the Pro version. Visit our website to learn more about the [WP Ultimate CSV Importer Pro features](https://www.smackcoders.com/wp-ultimate-csv-importer-pro.html?utmsource=web&utmcampaign=readme&utmmedium=wporg).

= How Can I Migrate Data from One Site to Another? =
To migrate data, follow these steps:  
* Export the data from your source site in CSV or XML format using the plugin's export functionality.  
* Review and edit the exported file if necessary to ensure data accuracy before migration.  
* Import the data into your target site using the plugin.  

For exporting, you'll need the WP Ultimate Exporter add-on, which enables the data export functionality. The add-on can be easily installed from the WordPress plugin repository.

= How Do You Import All Data with the WordPress Importer? =
You can effortlessly import all your data, including posts, pages, products, and custom fields (supported fields only), with our WordPress importer. Simply upload your CSV or XML file, configure the post type, and the plugin will automatically map your file fields to the corresponding WordPress fields. Click Import and your data will be smoothly imported.

= How Do You Import Images Using the WP Import Export Plugin? =
The WP Import Export plugin makes it easy to import images alongside your content. You can link images to posts or products and enhance SEO by adding alt text and titles during the import process.


== Installation ==
It is as simple as installing any other WordPress plugin. There are two general methods:

= From Admin Page =
 Go to Plugins -> Add New
 Search for WP Ultimate CSV Importer
 Click Install Now -> Activate


== Changelog ==

= 7.18 = 
Release date: 2025-03-04  
New Feature: Added compatibility for Secure Custom Fields.  
New Feature: Added support for XLS & XLSX import and export.

= 7.17 =
Release date: 2025-02-18
New Feature: Added import support for WooCommerce Customers module.
Enhancement: Improved UI for a better user experience.
Fixes: Minor bug fixes for better performance.

= 7.16 =
Release date: 2025-02-04
Added: Single CSV Import for WooCommerce product variations.

= 7.15 =
Release date: 2025-01-28
Added: Import support for WooCommerce orders, including personalized product details from Extra Product Options For WooCommerce fields.

= 7.14 =
Release date: 2025-01-20
 Added: Single Page CSV Export & Import directly from the post edit view

= 7.13.3 =
Release date: 2025-01-06
 Added: support for importing Jet Reviews data. 
 Fixed: Term & Taxonomies Hierarchy import issue 
 
= 7.13.2 =
Release date: 2024-12-18
 Fixed: Polylang import issue
 Fixed: Format issues with XML rss feed
 Fixed: TypeError occured during xml import * php 8.2

= 7.13.1 =
Release date: 2024-12-10
 Added: support for importing Jet Booking data.  
 Added: compatibility with WooCommerce EAN/GTIN fields.

= 7.13 =
Release date: 2024-11-27
 Added: Import/Export support for listings in Business Directory Plugin.
 Added: Import/Export of listings in Advanced Classifieds & Directory Pro.
 Added: Import/Export compatibility for Places in GeoDirectory.

= 7.12.2 =

 Compatibility Updates: Verified and ensured compatibility with WordPress version 6.7.
 Added: Support for JetEngine's advanced date field, allowing users to handle complex date data more effectively.
 Fixed: Various bugs and enhanced the Help section for a smoother user experience.

= 7.12.1 =
Release date: 2024-11-06
 Added: Support for importing RankMath dataset schema-type fields.
 Added: Import of array/object custom fields in JSON format.

= 7.12 =

 Added: Import support for WooCommerce orders, including personalized product details from PPOM (Personalized Product Option Manager) fields.
 Added: Export support for WooCommerce orders, with personalized product information generated from PPOM fields.

= 7.11.10 =

 Added: Support for importing and exporting JetEngine Custom Content Types (CCTs).

= 7.11.9 =

 Added: Support for YITH Barcodes and Cost of Goods plugin.
 Added: Import support for ACF Dashicon Field.

= 7.11.8 =

 Added: Introducing WooCommerce Orders, Coupons import and export.
 Improved: WooCommerce Products import and export via API.
 Fixed: JetEngine Custom Fields import issues.
 Resolved: Minor bugs.

= 7.11.7 =

 Added: Support for WooCommerce Reviews Import.
 Fixed: Minor issues in the media section.

= 7.11.6 =

 Fixed: Custom field value with serialized format issue
 Resolved: Missing pause resume option issue

= 7.11.5 =

 Added: Language translation support for Media section content

#### Minor Enhancements

 Improved handling of zip image uploads
 Enhanced dropdown functionality for custom post types
 Updated display for the media tab screen
 Added reporting for failed image URLs with status codes 401, 403, 408, 502, and 504
 Corrected media log and summary path

= 7.11.4 =

#### Enhancements

 CSV Export for Media Details: Users can now download a comprehensive CSV file after importing media. This CSV includes columns for status, mediaid, mediaurl, filename, title, caption, alttext, and description.
 CSV Export for Post Failed Image Details: Users can now download a detailed CSV file after importing posts. This CSV includes columns for mediaid, posttitle, postid, and actualurl.
 Added New Media Import Tab: A new tab has been introduced, dedicated to media import and updates. This feature allows users to upload both local and external images directly.
 Featured Image Metadata: Added new metadata options in the mapping section, including imagetitle, imagefilename, imagealttext, imagedescription, and imagecaption.
 Enhanced Logging: Improved logging details for more comprehensive tracking of import and media processes. Post-import logs now display the count of images associated with the post and the count of any failed images.
 Log Manager Improvement: Implemented a Log Manager where users can access and download summary logs, media logs, and failed media logs as CSV files. 

= 7.11.3 =

 Added: Import support for Toolset Types plugin basic fields.
 Fixed: Posttitle mandatory not display in drag and drop section.
 Fixed: Pods relationship field front end display issue.
 Fixed: Product category image import issue.

= 7.11.2 =

 Fixed: PHP Custom function imports getting 500 error issue.
 Fixed: Resolved issue with one column csv file import.
 Fixed: JetEngine relation post not assign issue.
 Fixed: ACF taxonomy field and Relationship field value stores db issue.

= 7.11.1 =

 Added: Introducing the Elementor templates bulk export (addon) and import feature in a single CSV file format. This allows you to back up your Elementor templates as an easily portable single CSV file.
 Added: Introducing content import support for Posts, Pages, Custom Post Types (CPTs), and WooCommerce Products designed with Elementor, simplifying the content migration process for Elementor websites.

= 7.11.0 =
Added: Introducing a new WPML import feature, enabling users to import multilingual content as posts and pages using the WordPress CSV Importer plugin for both CSV and XML files.


= 7.10.21 =
 Added: Implemented one-to-one relationship for JetEngine meta fields
 Fixed: Resolved issue with exporting data using authors' advanced filter.
 Fixed: Resolved issue with the specific authors select box.

= 7.10.20 =
 
 Fixed: Security vulnerability issue.
 Fixed: PHP Deprecated issue with optional parameters $unikeyvalue, $unikeyname, $linenumber.
 Fixed: Import back button from mapping section skips previous stage.
 Fixed: Plugin language loads from the site language not admin language

= 7.10.19 =
 
 Fixed: undefined variable: $importtype.
 Fixed: issue with XML file upload error in PHP 7.4 & PHP 8.1
 Fixed: deprecated function warnings (autodetectlineendings, SpecificStatus, SpecificAuthor, & SpecificPeriod).
 Fixed: import issues for draft pages.
 Fixed: prefix value empty notice on the settings page.
 Fixed: array conversion error
 Fixed: null array access error.
 Fixed: import issues for math functions & custom functions.
 Fixed: post content formatting.
 Fixed: import of tab-separated CSV files.

= 7.10.18 =
 
 Fixed: term import with same slug name different taxonomy issue

= 7.10.17 =
 Fixed: category hierarchical import issue
 Fixed: Undefined variable $nextnotice issue warning
 Fixed: addon section and title alignment post-install view
 Added:  translation for upgrade notice message
 Added: popup box for all pro button
 Added: header and back button to main page addon section for Plugin active functionality
 Updated: background color to light and professional across all areas

= 7.10.16 =

 Fixed: unwanted widget showing the issue
 Fixed: LifterLMS course multiple instructor import  issue
 Fixed: Mapping section popup 

= 7.10.15 =

 Fixed: Resolved Lifter LMS issue related to courses import.
 Fixed: bug with Lifter LMS instructor field import 
 Fixed: activation conflict when the Meta Box plugin is active.

= 7.10.14 =
 
 Fixed: XML import issue and error.
 Updated: Event Manager (Events) Import support.
 Fixed: Polylang issue resolved.

= 7.10.13 =
 
 Fixed: minor bug with the import process
 Fixed: UI & cosmetic issues

= 7.10.12 =
 
 Fixed: FIFU support for custom post type.
 Fixed: PHP deprecated error.
 Removed: Additional carriage returns.
 Fixed: WooCommerce postdate import & product count issue.
 Fixed: Custom Post Type (CPT) product count display showing '0' issue.
 Fixed: Email Subscription issue

= 7.10.11 =
 
 Fixed: minor bug related to the evaluation function
 Fixed:  an issue where image URLs containing spaces were not imported correctly.
 Fixed: the problem causing the admin-view to malfunction after image import.

= 7.10.10 =
 
 Removed: Unwanted Upgrade Notice Removed

= 7.10.9 =

 Added: Polylang multilingual support
 Fixed: Elementor Style Import Issue
 Fixed: Custom taxonomy import issue Created by CptUI plugin
 Fixed: Metabox custom field import issue which is assigned to taxonomy
 Added: support for image, tag, category
 Added: export support for tag, category

= 7.10.8 =

 Fixed: Addon vulnerability fix

= 7.10.7 =

 Fixed: RankMath SEO Import Issue
 Fixed: All-in-one SEO Import Issue
 Fixed: Yoast SEO Import Issue
 Added: Language support for New Zealand, Australia, Pirate, Polish

= 7.10.6 =

 Added: WP Version 6.4.2 compatibility
 Modified: Minor UI changes
 Fixed: Support for all postdate formats

= 7.10.5 =

 Resolved: posttitle mandatory bug in advanced mode

= 7.10.4 =

 Added: ChatGPT integration for content and featured image

= 7.10.3 =

 Fixed: WP Error when import empty post title.

= 7.10.2 =

 Added: Bulk Import & Export Support for Elementor Templates 

= 7.10.1 =

 Added: WP Version 6.4.1 compatibility

= 7.10.0 =

 Added: Support for ACF Bidirectional Relationship Import (User, Post Object, Relationship, Taxonomy)

= ******** =

 Added: Restriction added for Masterstudy LMS Import & Export

= ******** =

 Added: WP Version 6.3.2 compatibility
 Added: Exmage image import support 

= ******** =

 Added: Import support for WPComplete plugin

= ******** =

 Fixed: Minor bugs in the meta box checkbox list
 Updated: admin notice

= ******** =

 Fixed: Minor bugs in totalpress import
 Updated: Banner image 

= ******** =

 Added: Support for TotalPress Custom Post Types and Fields Import & Export

= ******** =

 Fixed: Minor bugs 
 Removed: Unwanted code

= ******** =

 Added: Support for CFS loop fields import

= ******** =

 Added: WP Version 6.3 compatibility
 Fixed: CFS Import issue

= 7.9.10 =

 Fixed: Delete from WordPress * XML limit issue
 Fixed: 500 issues

= 7.9.9 =
 
 Added: Yoast SEO Free import and export support
 Updated: Security fix * Restriction in import folder indexing
 Removed: Author/Editor import option from settings for security reasons

= 7.9.8 =

 Fixed: Restricted advanced manipulation options for author role 
 Updated: Security fix * Restriction in export folder indexing

= 7.9.7 =

 Fixed: JetEngine default time import

= 7.9.6 = 

 Added: ACF Basic Fields under Taxonomies 

= 7.9.5 = 

 Resolved: ACF Fields Missing under Users Module

= 7.9.4 = 

 Resolved: Issue with Upload txt file via FTP, SFTP sections

= 7.9.3 =

 Modified: Media table image url data type 
 Fixed: WooCommerce Stock Status issue 

= 7.9.2 =

 Modified: Displays plugin menu based on the selected user roles

= 7.9.1 =

 Fixed: WooCommerce bundle product sort order issue
 Added: MetaBox plugin's video field import support

= 7.9 =

 Added: Support for BuddyPress Users Import

= 7.8 =

 Fixed: PHP 7.4 Warnings & Notices & Deprecated Errors
 Fixed: Minor bugs in MasterStudy LMS Import

= 7.7 =

 Added: Support for FIFU (Featured Image From Url) import and export
 Cleared: PHP 7.4 Warnings & Notices

= 7.6 =

 Added: Support for MasterStudy LMS plugin: Courses, Lessons, Quizzes, Questions, Orders
 Checked: 6.2 version compatibility
 Fixed: Minor bugs

= 7.5 =

 Added: Support for Lifter-LMS plugin: Courses, Coupons, Reviews, Lessons
 Fixed: Missing language translations
 Fixed: XML Zip file upload

= 7.4 =

 Fixed: Turkish & Chinese language translations
 Added: WooCommerce Orders, Coupons & Refunds export support

= 7.3 =

 Added: SEOPress import integration with complete support
 Fixed: UI/UX issues and minor bugs
 Fixed: Now install and manage add-ons directly from the plugin interface

= 7.2 =

 Added: options for WooCommerce Orders exporter addon
 Added: options for WooCommerce Coupon exporter addon
 Added: options for WooCommerce Refund exporter addon
 Added: Usability and UI Improvements
 Added: Translation added for missing strings for 4 languages
 Checked: 6.1.1 version compatibility

= 7.1 =

 Resolved: PHP Version 8.0 check array values
 Checked: Compatibility for WordPress 6.1
 Changed: XML limits for files with fewer than 10 records to three

= 7.0.1 =

 Added: Arabic language
 Added: Persian language
 Added: Language support for Chinese(Simplified)
 Added: Language support for Tamil

= 7.0 =

 Added: Support for Advanced Custom Fields  Basic, Choices, and JQuery Fields
 Checked: Compatibility for WordPress 6.0.2

= 6.5.8 =

 Security Fix  Reported by Sanjay Das Payatu

= 6.5.7 =

 Added: Support for Jet Engine Basic Fields

= 6.5.6 =

 Checked: Compatibility for WordPress 6.0.1
 Modified: Minor UI changes

= 6.5.5 =

 Added: support for the Pods plugin

= 6.5.4 =

 Added: support for the MetaBox plugin

= 6.5.3 =

 Fixed: Added Support for default Categories and Tags

= 6.5.2 =

 Fixed: Restricted the internal URL upload process

= 6.5.1 =

 Updated: Documentation section links
 Updated: Module name from export section

= 6.5 =

 Checked: Compatibility for WordPress 5.9.3
 Added: Support for WP Job Manager.

= 6.4.4 =

 Checked: Compatibility for WordPress 5.9
 Fixed: Comment content import.

= 6.4.3 =

 Updated: All CSS and JS libraries to the latest version.
 Added: Proper sanitizing and escaping.
 Fixed: Calling files remotely
 Removed: wp-load.php and replaced with WordPress Ajax call.
 Fixed: Combining and/or Renaming Javascript Files

= 6.4.2 =

 Added: CSRF Protection
 Fixed: Security Issues (Removed curl, bitly urls, and filegetcontents for remote URL instead of using HTTP API)
 Updated: Bootstrap CSS and JS libraries to the latest.
 Verified: Nonce Check.

= 6.4.1 =

 Fixed: Vulnerability Issue in Zip File Upload
 Updated: Exporter Pro Buy Now and Upgrade to Pro price changes

= 6.4 =

 Added: Support for latest LearnPress-LMS plugin-V4.1.4.1: Courses, Lessons, Quizzes, Orders, and Questions.

= 6.3 =

 Checked: Compatibility for WordPress 5.8.2
 Updated: Third-party CSS and JS libraries

= 6.2.9 =

 Added: Support for latest WooCommerce product bundle plugin-v6.12.4
 Fixed: Dropbox link image issue
 Fixed: Language  English(South Africa) issue

= 6.2.8 =

 Added: support for an image through a Dropbox link
 Checked: Compatibility for WordPress 5.8.1

= 6.2.7 =

 Added: Upgrade now for the latest updates.
 Added: Delete records not present in XML, CSV files  settings option
 Added: Separate widget for WooCommerce product attributes
 Added: List WooCommerce Product Variations in the dropdown, on clicking display update to pro alert (toastify)
 Fixed: Support page mail issue
 Fixed: Post content image issue

= 6.2.6 =

 Added: Support for URL and FTP file upload
 Added: WordPress 5.8 Compatibility
 Fixed: JQuery droppable issue

= 6.2.5 =

 Added: Compatibility for WordPress 5.7.2
 Added: Support for Polylang Free & Polylang for WooCommerce plugin.
 Added: WP Custom field widget in advanced mode
 Fixed: Default custom field case-sensitive issue in creating wp custom field widget

= 6.2.4 =

 Added: Compatibility for WordPress 5.7
 Added: Compatibility for All in One SEO latest version 4.1.0.2

= 6.2.3 =

 Added: Compatibility for WordPress 5.7
 Added: Support for Rank Math SEO PRO Plugin.

= 6.2.2 =

 Added: support for Rank Math SEO Plugin.

= 6.2.1 =

 Added: Compatibility for WordPress 5.6
 Added: PHP 8 compatibility
 Added: Delete items not present in the XML, and CSV files while importing.

= 6.2 =

 Added: Simple Mode Method.
 Added: Support for WooCommerce Product Bundle Fields.

= 6.1.9 =

 Added: Language Compatibility for French, Italian, German, Spanish, Japanese, Russian, Dutch, Turkish, English(Canadian), English(British), and English(South African).

= 6.1.8 =

 Added: Compatibility for WordPress 5.5.1
 Added: Support for Variation Swatcher for WooCommerce Plugin.

= 6.1.7 =

 Added: Support for WC Product Bundle Meta Fields.

= 6.1.6 =

 Added: Mandatory fields for posts, pages, custom posts, and categories.
 Added: Recommended addon setup page.
 Fixed: single row WordPress XML import.
 Fixed: Page template import.

= 6.1.5 =

 Added: Support for Multi-Language plugin(Posts, Pages, and Custom Posts).
 Added: Post Expirator Support for Posts, Pages, and Custom Posts.

= 6.1.4 =

 Added: Support for Widgets import(Posts, Pages, Comments, Categories, and Archives).
 Added: Separate Media Handling Section.
 Fixed: CSV Headers with whitespace issue.

= 6.1.3 =

 Fixed: BBPress issues.
 Added: Support for navigation menus.

= 6.1.2 =

 Added: Support for BBPress Plugin(Topic, Reply, Forum)
 Fixed: ispluginactive check condition.

= 6.1.1 =

 Added: Post Parent and Menu Order field support for Custom Posts
 Added: CSRF Protection

= 6.1 =

 Added: support for LearnPress-LMS plugin: Courses, Lessons, Quizzes, Orders, and Questions.

= 6.0.9 =

 Fixed: image extension issue.
 Added: Comment reply support.

= 6.0.8 =

 Added: XML supports more than one header mapping.
 Added: posttrash and delete status import.

= 6.0.7 =

 Added: Imports the post-content image into the media library.
 Added: featured image based on ID.

= 6.0.6 =

 Added: Existing media image with URL and image name.
 Fixed: WordPress XML import poststatus publish issue.

= 6.0.5 =

 Added: XML Support for posts, pages, custom posts, and users.
 Fixed: post-format issue.
 Fixed: Deprecated: Non-static method.

= 6.0.4 =

 Renamed: assets/admin.js to admin-v6.0.4.js (To avoid browser cache problem).
 Fixed: Email validation in support form(gmail.co.uk).
 Added: Language support for the Spanish language.
 Fixed: Resume/pause timer issue.

= 6.0.3 =

 Added: postcontent image without downloading into media option.
 Added: postcontent editor in drag and drop mode.
 Fixed: Bulk import problem.
 Fixed: CSV and text file support with accepted delimiters are, , \t , | , ; , :
 Fixed: plugin home page UI issue with PHP version 5.5

= 6.0.2 =

 Fixed: Post imports struck in the first record.
 Added: support for images through Google Drive.
 Fixed: CPT-UI with product category issue.
 Fixed: minor issues & warnings.

= 6.0.1 =

 Added: Upload support for the Txt file.
 Improved: UI Improvements  process image for in send button on the support page.
 Improved: UI  Show export tab by default. If the user has not installed the exporter plugin, It contains the download link.
 Improved: UI  In the plugins page inside CSV importer. If a plugin is installed, show it as already installed.
 Fixed: Timer not stopping after import.
 Modified: Changed video URL in the documentation. In settings changed fileuploads and allowurlfopen to on instead of 1.

= 6.0 =

 Improved: User interface and performance.
 Added: Header Manipulation fields can now hold static text content along with any CSV header content.
 Added: Media upload section to import every Images from the computer.
 Added: Progress bar for desktop upload.
 Fixed:  WordPress core custom fields issue.

= 5.6.2 =

 Added: Support for remote URLs without extensions.

= 5.6.1 =

 Added: WP CSRF Protection.

= 5.6 =

 Added: Compatibility for WordPress 5.0

= 5.3.7 =

 Removed: Registering custom field in mapping section.

= 5.3.6 =

 Added: Compatibility for WordPress 4.9.8
 Added: Post parent imports with Post Title
 Fixed: Post-export redirection issue.
 Fixed: Post slug import in drag and drop method.

= 5.3.5 =

 Added: Support for serialized data import.
 Added: Compatibility for WordPress 4.9.6
 Improved: Usability and user interface.
 Fixed: Insertion of hyperlinks in WYSIWYG editor in drag and drop mapping.

= 5.3.4 =

 Improved: Notifications in mapping.
 Fixed:  Export menu issue.

= 5.3.3 =

 Added: Compatibility for WordPress 4.9
 Moved: Users import as an add-on.
 Removed: Export

= 5.3.2 =

 Added: Add-ons support
 Moved: User import as an add-on.
 Removed: Export

= 5.3.1 =

 Modified: Hide the filter based on Condition.
 Fixed: Data loss when page refresh (export).
 Fixed: Warning during Upload.
 Fixed: WP user export above 1000 records.
 Fixed: Custom Field Suite issue.
 Fixed:  Forced quotes issue.

= 5.3 =

 Added: Prevent the loss of mapping data
 Added: Custom field group plugin support
 Added: Maintenance mode
 Added: Inclusion feature
 Updated: Exclude selection as included selection in the export module
 Fixed:  User import
 Fixed:  Delimiter issue
 Fixed:  Advance mapping issues
 Fixed:  Post status in Mapping
 Fixed:  Featured image in Mapping
 Fixed:  Post comment in Mapping
 Fixed:  Export page radio button based on plugin activation
 Fixed:  Comment Export
 Fixed:  Text changes

= 5.2 =

 Added: Advanced mapping view with Drag and Drop support.
 Added: Ultimate member plugin support for user import.
 Fixed:  Issue with Post format.
 Fixed:  Month order in dashboard charts.
 Added: Latest version support on All in One SEO ********
 Added: Compatibility for WordPress 4.7.3

= 5.1.1 =

 Fixed:  Broken when SCRIPTDEBUG is true. [Solved](https://wordpress.org/support/topic/broken-when-scriptdebug-is-true/).
 Fixed:  Issue in duplicate handling to skip the duplicate records.
 Added: Missing font "glyphicons-halflings-regular.woff2".
 Removed: Unwanted console warnings.
 Added: Compatibility for WordPress 4.7.2

= 5.1 =

 Added: Language Support for German, French, Italian, Spanish, and Dutch & Russian.
 Added: Restriction to show Admin dashboard widget only for users with Admin role. [Solved](https://wordpress.org/support/topic/admin-dashboard-widget-is-showing-to-all-users)
 Added: Notice to [enable wp-cron](https://www.smackcoders.com/blog/enable-configure-wp-cron.html?utmsource=wporg&utmcampaign=readme&utmmedium=changelog) to populate the feature images. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
 Added: Warning messages to notify when the uploads directory is missing, insufficient permission to access, and the uploaded file size exceeds your server limits.
 Added: Duplicate handling feature to skip duplicate records.
 Added: Canonical URL support in All in One SEO data import.
 Improved: CSV export performance.
 Fixed:  All custom fields in WP installation added to a Post. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
 Fixed:  Mixing up of Custom taxonomies while assigning a term to Post. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
 Fixed:  Adding unwanted data before and after post content. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
 Fixed:  Issue with Post Category & Tags export.
 Fixed:  Missing up on SEO fields in the mapping section.
 Fixed:  Issue in exporting All in one SEO field.
 Fixed:  Issue in assigning page template (wppagetemplate).
 Removed: Warnings in migration script.

= 5.0.3 =

 Added: Support for traditional Chinese characters.
 Fixed:  Author/Editor menu visibility issue
 Fixed:  [Assigning categories to post issue](https://wordpress.org/support/topic/ultimate-csv-importer-v5-adds-all-posts-to-uncategorized/)
 Fixed:  [Import with Chinese character](https://wordpress.org/support/topic/unable-to-import-posts-with-chinese-characters/)

= 5.0.2 =

 Added: Compatibility from PHP 5.3.

= 5.0.1 =

 Fixed:  WP Customer Reviews import feature.

= 5.0 =

 Added: Compatibility for WordPress 4.7 and PHP 7
 Added: Option to replace imported CSV file value with static and dynamic value.
 Added: Image imports from external URL.
 Added: Send email to newly imported User with Password Information
 Added: Any Custom Post Type import.
 Added: Post Type imports with terms & and taxonomies with any depth of the parent-child hierarchy.
 Improved: High-speed import with enhanced UI.
 Improved: User role import with capability value or role name in CSV.

= 3.11.1 =

 Fixed:  Browse button disappears in 3.11.0 https://wordpress.org/support/topic/browse-button-disappears-in-3110

= 3.11.0 =

 Added: Compatibility for WordPress 4.5.3
 Added: menuorder field Import for Custom Post Type.
 Added: [Support for comma and pipeline separation in multi-category & taxonomies import](https://wordpress.org/support/topic/importing-a-taxonomy-field?replies=4).
 Added: Compatibility to export WooCommerce fields with WooCommerce version 2.6.1
 Updated: Help links of Product page and live Demo.
 Fixed: Issues in Taxonomies, Categories & Tags export.
 Fixed: Issue in export by the status filter.

= 3.10.0 =

 Improvements: Can export any number of records from the WordPress site.
 Fixed:  Issue in ACF relationship field export.

= 3.9.4 =

 Improvements: Duplicate image handling. Option to skip or rename image as imagename-number if image name is same as existing media image name.

= 3.9.3 =

 Added: Compatibility for WordPress 4.5
 Fixed:  Environment issue with custom port ID in MAMP.

= 3.9.2 =

 Modified: CSV Parser Engine with Smack CSV Parser, a high-speed robust parser.

= 3.9.1 =

 Added: Post parent now supports post title and post name.
 Fixed:  jQuery conflicts.

= 3.9 =

 Added: PHP 7 compatibility.
 Added: Support for all postdate formats.
 Fixed: Featured image and Inline image naming issues.
 Fixed: Auto mapping issues in Custom Fields and SEO Fields.

= 3.8.8 =

 Added: Localize script for multi-language support.
 Added: WordPress 4.4.1 compatibility.
 Improved: Code cleanups with WordPress standards.
 Fixed: Vulnerability security issue.
 Fixed: Export issue.
 Fixed: Custom Taxonomy import issue.
 Fixed: User mail notification on new user imports.
 Fixed: Category & Tag issue in the eShop module.
 Removed: Mod security check.

= 3.8.6 =

 Added: Compatibility for WordPress 4.4
 Modified: Support page UI.
 Fixed:  Postdate issue.
 Fixed:  Custom Post Type listing issue.

= 3.8.5 =

 Added: Restriction to view the image without a password for protected status content.
 Modified: Settings page UI.
 Fixed:  poststatus mandatory validation issue.
 Fixed:  SEO Fields mapping issue.
 Fixed:  Known issues in export.
 Fixed:  Mandatory validation issues.
 Fixed:  Console Type Error issue.

= 3.8.4 =

 Modified: Changed the Dashboard view.

= 3.8.3 =

 Added: Text domain for the language translation system.
 Fixed: Detected duplicate issue.

= 3.8.2 =

 Added: Compatibility for WordPress 4.3.1
 Added: Grouped core custom field in mapping section.
 Added: Image import with spaces in the image name.
 Fixed: Module entry counts in dashboard issue.
 Fixed: Duplication of the image in the media gallery.

= 3.8.1 =

 Added: Compatibility for WordPress 4.2.3 and 4.2.4
 Added: Export by a specific date and author option in comments.
 Fixed: Warnings triggered in the console.
 Fixed: XSS vulnerability.
 Removed : ../../../../../../wp-load.php and replaced with WordPress Ajax call.
 Removed: Direct usage of wp-content.

= 3.8 =

 Added: Multi language support(frFR,esES,nlNL).
 Added: Inline image handling with short code along with image attributes.
 Added: Any delimiter support for CSV export.
 Fixed:  Warnings and bug fixes.

= 3.7.4 =

 Added: WordPress 4.2.2 compatibility.
 Fixed: Allow Editor/Author to import(Multisite also).

= 3.7.3 =

 Fixed:  Vulnerability security issue.

= 3.7.2 =

 Added: WordPress 4.2 and 4.2.1 compatibility.
 Fixed:  Blank page issue conflicts.

= 3.7.1 =

 Added: Security fix for curl.
 Added: Security fix for session status.

= 3.7 =

 Fixed:  Featured image hotlink issue.

= 3.6.78 =

 Added: Hot security fix in readfile.php.
  
= 3.6.77 =

 Added: WordPress 4.1.1 compatibility.
 Improved: Inline image feature.
 Added a recursive method to assign the image.
 Fixed:  Featured image naming issue. [Solved](https://wordpress.org/support/topic/problem-in-import-with-the-image-name)
 Removed: Warnings.

= 3.6.76 =

 Improved: Post Format.
 Fixed:  Export eShop content issue.
 Fixed:  Import with image name issue.
 Fixed:  Groups plugin conflict.

= 3.6.75 =

 Added: Terminate & Continue option in import.
 Improved: Log section.
 Fixed:  Web View & Admin View issue.
 Fixed:  Security issue in the export module.

= 3.6.74 =

 Fixed:  Security issue.

= 3.6.73 =

 Added: WordPress 4.0 compatibility.
 Added: HTTPS format support for all WP instances.
 Added: Warning to guide the user to create an uploads directory with writable permissions.
 Improved: Security and performance tab under settings module.
 Fixed: Featured image-handling issues.
 Fixed: Multisite compatibility issue.
 Fixed: All console warnings.
 Removed: Post Content field mandatory option.

= 3.6.72 =

 Added: Debug mode enable/disable options.
 Modified: Menu order changes.

= 3.6.71 =

 Fixed:  Minor bugs.

= 3.6.7 =

 Added: Export features for all missing modules.
 Fixed:  All console warnings and reported logs.

= 3.6.6 =

 Fixed:  Dashboard chart issue in multisite.
 Modified: UI to improve usability.
 Fixed:  Groups plugin conflicts.

= 3.6.5 =

 Added: Inline image support with advanced media handling.
 Added: PDO check.

= 3.6.4 =

 Added: WordPress 4.0 compatibility.
 Added: Advanced export features with filter options.
 Improved: Advanced log section.
 Fixed:  jQuery issues.

= 3.6.3 =

 Added: eShop support.
 Added: WordPress 3.9.2 compatibility.
 Fixed:  Conflicts with other plugins.

= 3.6.2 =

 Fixed:  Hot security issue.

= 3.6.1 =

 Fixed:  Multi-site support issue.
 Fixed:  Duplicate issue.
 Fixed:  Security issue.

= 3.6 =

 Added: Interactive graphs and charts in the plugin dashboard.
 Added: Admin dashboard widgets.
 Added: Users and comments export feature.
 Added: Auto delimiter handling.
 Added: Auto mapping feature.
 Added: Allow authors to access import features.

= 3.5.5 =

 Added: postformat attribute support.
 Added: pagetemplate attribute.
 Added: updatepostmeta for duplicate meta issue
 Fixed: Type Error issue in jQuery.

= 3.5.4 =

 Added: All-in-One SEO Pack support.
 Added: WordPress 3.9.1 compatibility.

= 3.5.3 =

 Added: Compatibility for WordPress 3.9
 Added: Export feature for Posts, Page, Custom Post.
 Fixed: Reported bugs
 Removed: All warnings.

= 3.5.2 =

 Posts with author names as numerical apart from the User ID.
 Added: menuorder attribute import.
 Added: Auto image rename option.
 Option to cancel any partial import in the middle.
 Improved image handling even special characters in URL.
 can handle image URLs without any extensions.
 User-reported Bugs Fixed.

= 3.5.1 =

 User-reported issue fixes.
 Activation and other plugin conflict issues solved like Jetpack.
 Admin UI freezing issues  screen option, help links issues fixed.
 Fixed: WYSIWYG editor UI issue.

= 3.5.0 =

 Combined major release versions of 3.5 and 3.4
 Improved MVC structure.
 Improved User interface with drag and drop feature.
 Improved: WordPress 3.8.1 compatibility added.
 Module-based systems allow simplifying UI.
 Added: Detailed log feature added.
 Added: Support and Useful links added.
 Added: Support is made easy now from the plugin.

= 3.3.1 =

 Added: Multisite compatibility except for User import.
 Added: Comments, Users modules mandatory fields validation added.
 Improved: Removed unwanted warnings.

 = 3.3.0 =

 Added: WordPress 3.8 compatibility.
 Added: Bulk users with a role import feature.
 Added: Comments import feature with relevant post IDs.

= 3.2.3 =

 Added: WordPress 3.7.1 compatibility added.
 Added: Different media path support added.
 Added: Subfolder installations support added.
 Improved: Updated plugin directory path.
 Improved: Removed unwanted warnings.
 Improved: Performance check.

= 3.2.2 =

 Added: WordPress 3.6.1 compatibility added.
 Added: Mapping UI improved with on select dynamic update feature.
 Added: Help content added.
 Fixed:  Post slug issue Fixed and tested for 3.6 and 3.6.1

= 3.2.1 =

 Improved: Performance improvements on SQL and CSV parsing.
 Fixed: Plugin deactivation issue and updated the code.
 Fixed: Links in the cells make problems with the "quote".
 Fixed: Loading content from more than one column.
 Fixed: Custom Post type issues Fixed:

= 3.2.0 =

 Improved: User interface improvements.
 Improved: WordPress 3.6 compatibility added, Much Improved UI.
 Fixed:  Featured image issues fixed for WordPress-3.6

= 3.1.0 =

 Improved: Much Improved Featured Image feature.
 Fixed:  Image URL for featured image issues.
 Fixed:  PHP 5.4 upgrade fix

= 3.0.0 =

 Added: Category in numeric are restricted and skipped to Uncategorized
 Added: Protected status password inclusion as {password}.
 Added: Post authors can be User ID or name
 Improved: Much improved workflow
 Improved: Add a custom field option.
 Improved: Date format handling improved
 Improved: Any Date format supported now
 Improved: Future scheduling and status improved
 Improved: Can apply post status for the individual post via CSV itself
 Improved: Featured image handling improved and Fixed. More improvements are scheduled.
 Improved: Duplicate check options improved for both title and content options.
 Improved: Post author issue Fixed and improved
 Improved: Wrong userIDd or name is automatically assigned under admin
 Improved: Multi-category and tags improved
 Fixed: Custom Field mapping and import
 Fixed: Overall Status option improved and issue fixed.
 Fixed: Password field Fixed:  for Protected
 Fixed: Status as in CSV option improved and fixed.

= 2.7.0 =

 Added: Added more post-status options
 Added: Publish, Sticky, Private, Draft, and Pending Status for the whole import
 Added: Protected status with a common password option added
 Added: "Status as in CSV" to assign a status for the individual post through CSV as ID or Field Tag
 Added: User ID and User Name support for Post author feature added
 Added: In case of missing or false IDs post assigned to admin as draft
 Added: Add Custom Field Text box auto-filled with CSV header tag.
 Added: Duplicate detection for post content and post title added as options.
 Added: The user can choose either one or both to avoid duplicate issues.
 Improved: 6 Standard date format added as drop-down to choose.
 Improved: Renamed postname as postslug to avoid confusion
 Improved: Mapping Fields
 Improved: Field tags are formatted to support the auto-mapping option (next milestone)
 Improved: Listed custom fields with the prefix CF: Name for easy identification.
 Fixed: Date format conflict

= 2.6.0 =

 Fixed: Major Bug fixed.
 Fixed: Added UTF-8 support.
 Fixed: HTML tag conflicts.

= 2.5.0 =

 Major issues fixed and updated to WordPress-3.5.1 compatibility.

= 2.0.1 =

 Update to WordPress-3.5 compatibility.

= 2.0.0 =

 WPDEBUG errors 
 Fixed: The CSV import folder changed to the WP native uploads folder.

= 1.1.1 =

 Renamed the mapping field attachment as featuredimage and category as postcategory.

= 1.1.0 =

 Added featured image import feature along with post/page/custom post.

= 1.0.2 =

 Bug Fixed: To recognize the trimmed trailing space in the CSV file.
 Added: Validation for the duplicate field mapping.

= 1.0.1 =

 Added features to import multiple tags and categories with different delimiters.

= 1.0.0 =

 Initial release version. Tested and found works well without any issues.


== Upgrade Notice ==

= 7.18 =
Upgrade now to import and export XLS/XLSX files and gain compatibility with Secure Custom Fields.