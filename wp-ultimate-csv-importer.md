# Import and Export WordPress Data as CSV or XML

Contributors: smackcoders, smacksupport
Requires at least: 5.0
Tested up to: 6.0
Stable tag: 6.5.4
Version: 6.5.4
Requires PHP: 5.2.4
Author: smackcoders
Author URI: https://profiles.wordpress.org/smackcoders/
Donate link: https://www.paypal.me/smackcoders
Tags: csv, xml, csv import, xml import, csv importer, xml importer, import, import csv, import xml, import seo, import woocommerce, acf, excel, spreadsheet, pods, types
License: GPLv2 or later
Simple and powerful CSV / XML Import plugin even for advanced and novice users.

## Description

Now both CSV and XML import options available within the core plugin.

Ultimate CSV Importer provides all the necessary Import and Export features in one bundle with simplified steps to follow. Note the added export, woocommerce and User import feature available as add-ons, you can install below add-ons to benefit those features

1. [Export any WordPress data](https://wordpress.org/plugins/ultimate-exporter)
2. [Product Import Export for WooCommerce](https://wordpress.org/plugins/import-woocommerce/)

WooCommerce CSV Import Tutorial – Eay steps using WP Ultimate CSV Importer

[youtube https://www.youtube.com/watch?v=b3Ix0WB3Bwg&feature=youtu.be]

3. [Import Export WordPress Users](https://wordpress.org/plugins/import-users/)

How to import data to your WordPress?

There are 3 simple steps to follow,

1. Upload your CSV or XML file.
2. Relate or map your CSV or XML fields with WP fields using drag and drop or simple left/right pick list method.
3. Click Import once you finished your field mappings.

Overview: Whats new with WP Ultimate CSV Importer Latest version
[youtube https://www.youtube.com/watch?v=yg9Z6yJqRfo&feature=youtu.be]

There are more features bundled to make even complicated post data import simplified.

### 1.Upload your CSV/XML

- Upload your CSV or XML file from different sources like desktop, FTP/SFTP, remote URLs or a location from your host server.
- CSV, Zip, txt and XMl data (pro) file types supported.
- Once uploaded, choose New items if you want to import as new posts.
- Choose existing option in case to import the data to update or replace existing posts records.
- Pick a post type like Post, Page, Custom Post, Comments or any other from the drop down.
- This is under which post type your data gets imported.
- It can be a complete post content replacement or partial update like selected set of fields for each post in CSV/XML.
- Click continue button.

### 2.Relate or map your CSV or XML fields to WP fields

In mapping section, CSV or XML Importer plugin allows you to relate the uploaded data set against WordPress fields. There are two methods available to relate your data set.

- A simple left and right drop downs that allows you to map your CSV/XML data with the related WordPress fields.
- You can choose each field from left side and assign a field from right to where it should go.

### Drag 'n' Drop method

- You can visually drag field by field from the right side bar and drop it to post create view like place holder.
- For e.g. you can drag title filed and drop it under post title box.
- To make it easier you can see both field label and sample record in side bar, which allows you to understand how data placed.
- In top side bar header, you can rotate this sample records by choosing a row item no. in case the default sample data is not helpful.

### 3. Click Import

- Once the mapping completed, click import to start the import process.
- In import view, if required you can also pause and resume the import process.
- A section shows post count and status of successful imports with a timer.
- A log section shows you detailed real time log of the process step by step.
- Log is downloadable using the link provided.

### What can you import in WordPress

Core Modules – Post, Page, Custom Post, Comments.
Users – Import Users add-on to import your WordPress Users
Custom Post – CPT UI, Custom Press and default WordPress Custom Post.
Custom Field – WordPress default Text and Text area fields.
SEO field – Free version of All in One SEO.
Reviews – WP Customer Reviews

### Highlights

- High performance, much comparable & much faster than all the other available plugins.
- Can handle 1000s of records on the fly.
- Supports WordPress Custom Fields, Custom Press fields, WP Customer Reviews and All in One SEO fields.
- Media images from any external URL like Google Images, pixabay, shutterstock, or any domain you own can be imported.
- Image import processed from the background using WP-CRON to improve the performance.
- Import with duplicate handling for optimized database.
- Free add-on to Import Users <link> to upload user info into WordPress.
- WordPress core custom fields registered dynamically on the flow of CSV/XML import.
- Post type import with terms & taxonomies with any of depth of parent-child values.
- WordPress multisite import supported.
- Post Type CSV or XML import along with multi category & multi tag.
- Import CSV with any delimiter in UTF-8 format can be imported.
- Free export add-on to export <link> all your WP content in CSV file.

### Important Notes

- Featured image import from any publicly accessible external URL.
- All languages supported by WordPress imported in UTF-8 without BOM format.

### Other useful tools

- Dashboard
- General settings >> Scheduled log emails, send password in users get imported etc.
- Database optimization
- Security and performance monitoring
- Manager >>File manager, log manager
- Support >> Documentation

### Premium Features

- CSV and XML import
- Reusable Mapping Templates with Template manager
- Smart Schedule/Recurring Import to run import periodically without any manual interaction.
- Toolset Types Import – Custom fields, Post Relation, Intermediate post, Repeatable Field & Repeater Field Group support
- Multilingual import – support for the import of WPML & qTranslate X add-on
- WooCommerce product import along with six WooCommerce add-on
- Handles Custom Fields of ACF (FREE & Pro), Types, Pods and WordPress fields with the flow of import
- Import history i.e. detailed log maintained in the Log manager.
- Import all the supported features with Rest API.
- Extendable for your needs with API.

### HELPFUL LINKS

[Documentation](https://www.smackcoders.com/documentation/ultimate-csv-importer-pro/how-to-import-csv?utm_source=web&utm_campaign=readme&utm_medium=wp_org)

[Try our live demo here](https://demo.smackcoders.com/wordpress/wp-admin/admin.php?page=sm-uci-dashboard)

[Request a free trial by visiting here](https://www.smackcoders.com/wp-ultimate-csv-importer-pro.html?utm_source=web&utm_campaign=readme&utm_medium=wp_org) and Click Try Now option to generate your own WordPress test environment.

[For samples and tutorials, visit our blog](https://www.smackcoders.com/blog.html?utm_source=wp.org&utm_medium=plugin&utm_campaign=readme)

## Translations

Many thanks to the generous efforts of our translators:

- French (fr_FR) -- [the French translation team](https://translate.wordpress.org/locale/fr/default/wp-plugins/wp-ultimate-csv-importer)
- Italian (it_IT) -- [the Italian translation team](https://translate.wordpress.org/locale/it/default/wp-plugins/wp-ultimate-csv-importer)
- Spanish (es_ES) -- [the Spanish translation team](https://translate.wordpress.org/locale/es/default/wp-plugins/wp-ultimate-csv-importer)
- German (de_DE) -- [the German translation team](https://translate.wordpress.org/locale/de/default/wp-plugins/wp-ultimate-csv-importer)
- Japanese (ja) -- [the Japanese translation team](https://translate.wordpress.org/locale/ja/default/wp-plugins/wp-ultimate-csv-importer)
- Russian (ru_RU) -- [the Russian translation team](https://translate.wordpress.org/locale/ru/default/wp-plugins/wp-ultimate-csv-importer)
- Dutch (nl_NL) -- [the Dutch translation team](https://translate.wordpress.org/locale/nl/default/wp-plugins/wp-ultimate-csv-importer)
- Turkish (tr_TR) -- [the Turkish translation team](https://translate.wordpress.org/locale/tr/default/wp-plugins/wp-ultimate-csv-importer)
- English (en_CA) -- [the English (Canadian) translation team](https://translate.wordpress.org/locale/en-ca/default/wp-plugins/wp-ultimate-csv-importer)
- English (en_GB) -- [the English (British) translation team](https://translate.wordpress.org/locale/en-gb/default/wp-plugins/wp-ultimate-csv-importer)
- English (en_ZA) -- [the English (South African) translation team](https://translate.wordpress.org/locale/en-za/default/wp-plugins/wp-ultimate-csv-importer)
- French (fr_BE) -- [the French (Belgium) translation team](https://translate.wordpress.org/locale/fr-be/default/wp-plugins/wp-ultimate-csv-importer)

If you'd like to help out by translating this plugin, please [sign up for an account and dig in](https://translate.wordpress.org/projects/wp-plugins/wp-ultimate-csv-importer).

## Screenshots

1. Dashboard view of the importer.
2. Upload section of CSV or XML.
3. Simple Drag and Drop mapping.
4. Mapping view of the uploaded CSV or XML fields with WordPress fields.
5. Detailed real-time log.
6. Settings with php.ini details.
7. Downloadable log manager for all imported events.
8. XML Import Option.

## Frequently Asked Questions

- What is a CSV file?
  A CSV or comma separated value file is a simple text based file format to store tabular data. In general, a character like a comma used as a separator between commas separated values as tables.

- How to open or edit CSV file?
  A CSV file could be edited in any program like notepad or notepad+. However, a spreadsheet editor like MS Office, Open Office Calc is more useful to directly edit as tabled structure.

- What are the types of CSV files?
  Most common type is csv or comma separated value file. The other one is delimited file where a character like a tab or a semicolon used instead of comma as a delimiter.

- What is CSV file format?
  There are different file formats available based on encode like ANSI, UTF-8, UTF-8 without BOM, USC-2 BE BOM and USC-2 LE BOM. For WordPress, the default format will be UTF-8 and for non-Latin character data, UTF-8 without BOM used.

- How to validate a CSV?
  You can validate a csv file using [CSV Lint](https://csvlint.io/). Your CSV may miss escaping characters and misplaced/skipped character, which may cause the import to cease. Validate any csv file for issues or warning and fix them before importing it to WordPress.

- What is a XML file?
  An XML document is a basic unit of XML information composed of elements and other markup in an orderly package. An XML document can contains wide variety of data.

- How to import content to WordPress?
  Import to WordPress done in different methods based on content source type, platform, and file types. The most common types of imports are XML, CSV and DB files.

1. Xml method needs advanced knowledge to edit/update them in case of any changes required on source data. Otherwise, users are limited to do import without any changes to source file. Hence, editing done post by post in WordPress only possible after import.
2. CSV methods allow any users with basic excel or any spreadsheet knowledge can audit, change, edit or update the data visually in bulk before importing. So painful manual works avoided and time saved. Also, CSV file is the most preferred method for any offline programs.

- How to import bulk images as csv in wordpress

[youtube https://www.youtube.com/watch?v=6NqFduZUmHI&feature=youtu.be]

- How to handle Types Custom Fields Import in wordpress

[youtube https://www.youtube.com/watch?v=G_6vwYCDOOk&feature=youtu.be]

- How to import google sheets data to WordPress

[youtube https://www.youtube.com/watch?v=8P8xtRY3bCM&feature=youtu.be]

## Changelog
### 6.5.3 
* Fixed : Added import support for default Categories and Tags

### 6.5.2
- Fixed : Restricted the internal url upload process

### 6.5.1
- Updated : Documentation section links
- Updated : Modulename from export section  

### 6.5 
- Checked: Compatibility for WordPress 5.9.3
- Added: Import and Export Support for WP Job Manager.

### 6.4.4 
- Checked: Compatibility for WordPress 5.9
- Fixed: Comment content import.

### 6.4.3 
- Updated: All CSS and JS libraries to the latest version.
- Added: Proper sanitization and escaping.
- Fixed: Calling files remotely
- Removed: wp-load.php and replaced with WordPress ajax call.
- Fixed: Combining and/or Renaming Javascript Files

### 6.4.2 
- Added: CSRF Protection
- Fixed: Security Issues (Removed curl,bitly urls and file_get_contents for remote URL instead used http api)
- Updated: Bootstrap CSS and JS libraries to the latest.
- Verified: Nonce Check.

### 6.4.1
- Fixed: Vulnerability Issue in Zip File Upload
- Updated: Exporter Pro Buy Now and Upgrade to Pro Price changes

### 6.4

- Added: Import support for latest LearnPress-LMS plugin-V4.1.4.1:Courses,Lessons,Quizzes,Orders and Questions. 

### 6.3 

- Checked: Compatibility for WordPress 5.8.2
- Updated: Thirdparty CSS and JS libraries 

### 6.2.9

- Added: Import support for latest woocommerce product bundle plugin-v6.12.4 
- Fixed: dropbox link image issue 
- Fixed: Language - English(South Africa) issue    

### 6.2.8  

- Added: support for image import through dropbox link  
- Checked: Compatibility for WordPress 5.8.1

### 6.2.7 

- Added: Upgrade now for latest updates.
- Added: Delete records not present in csv/xml file - settings option
- Added: Separate widget for woocommerce product attributes
- Added: List Woocommerce Product Variations in dropdown, onclicking display update to pro alert (toastify)
- Fixed: Support page mail issue
- Fixed: Post content image import issue

### 6.2.6 

- Added: Support for URL and FTP file upload
- Added: WordPress 5.8 Compatibility 
- Fixed: Jquery droppable issue

### 6.2.5 

- Added: Compatibility for WordPress 5.7.2
- Added: Import support for Polylang Free & Polylang for WooCommerce plugin.
- Added: WP Custom field widget in advanced mode
- Fixed: Default custom field case sensitive issue in create wp custom field widget

### 6.2.4 

- Added: Compatibility for WordPress 5.7
- Added: Compatibility for All in One SEO latest version 4.1.0.2

### 6.2.3 

- Added: Compatibility for WordPress 5.7
- Added: Import support for Rank Math SEO PRO Plugin.

### 6.2.2 

- Added: Import support for Rank Math SEO Plugin.

### 6.2.1

- Added: Compatibility for WordPress 5.6
- Added: Delete items not present in the csv/xml file.

### 6.2

- Added: Simple Mode Import Method.
- Added: Import Support for WooCommerce Product Bundle Fields.

### 6.1.9

- Added: Language Compatibility for French,Italian,German,Spanish,Japanese,Russian,Dutch,Turkish,English (Canadian),English (British),English (South African).

### 6.1.8

- Added: Compatibility for WordPress 5.5.1
- Added: Import Support for Variation Swatcher for WooCommerce Plugin.

### 6.1.7

- Added: Import Support for WC Product Bundle Meta Fields.

### 6.1.6

- Added: Mandatory import fields for posts,pages,custom posts and categories.
- Added: Recommended addon setup page.
- Fixed: single row xml import.
- Fixed: Page template import.

### 6.1.5

- Added: Import Support for Multi Language plugin (Posts,Pages and Custom Posts).
- Added: Post Expirator Support for Posts,Pages and Custom Posts.

### 6.1.4

- Added: Support for Widgets import.(Posts,Pages,Comments,Categories and Archives).
- Added: Seperate Media Handling Section.
- Fixed: CSV Headers with whitespace issue.

### 6.1.3

- Fixed: BBPress Import issues.
- Added: Support for navigation menus.

### 6.1.2

- Added: Import Support for BBPress Plugin:(Topic,Reply,Forum)
- Fixed: is_plugin_active check condition.

### 6.1.1

- Added: Post Parent and Menu Order field import support for Custom Posts
- Added: CSRF Protection

### 6.1

- Added: Import support for LearnPress-LMS plugin:Courses,Lessons,Quizzes,Orders and Questions.

### 6.0.9

- Fixed: image extension issue.
- Added: Comment reply support.

### 6.0.8

- Added: Xml import supports with more than one header mapping.
- Added: post_trash and delete status import.

### 6.0.7

- Added: Import post-content image into media library.
- Added: Import featured-image based on ID.

### 6.0.6

- Added: Existing media image import with url and image name.
- Fixed: XML import-post_status publish issue.

### 6.0.5

- Added: XML-Import support for posts,pages,custom posts and users.
- Fixed: post-format import issue
- Fixed: Deprecated: Non-static method

### 6.0.4

- Renamed: assets/admin.js to admin-v6.0.4.js (To avoid browser cache problem)
- Fixed: Email validation in support form(gmail.co.uk)
- Added: Language support for spanish language
- Fixed: Resume/pause timer issue

### 6.0.3

- Added: Import post_content image without downloaded into media option
- Added: post_content editor in drag and drop mode
- Fixed: Bulk import problem.
- Fixed: csv and text file import support with accepted delimiters(, , \t , | , ; , :)
- Fixed: plugin home page ui issue with php version 5.5

### 6.0.2

- Fixed: posts import struck in first record
- Added: support for image import through google drive
- Fixed: CPT-UI with product category import issue
- Fixed: minor issues & warnings

### 6.0.1

- Added: Upload support for txt file
- Improved: UI Improvements - process image for in send button in support page
- Improved: UI - Show export tab by default. If user have not installed the exporter plugin, It contains the download link.
- Improved: UI - In plugins page inside csv importer. If a plugin is installed show it as already installed.
- Fixed: : Timer not stopping after import issue.
- Modified: Changed video url in documentation. In settings changed “file_uploads” and “allow_url_fopen” to “on” instead of 1.

### 6.0

- Improved: user interface and performance.
- Added: Header Manipulation fields can now hold static text content along with any CSV header content.
- Added: Media upload section to import Image from computer.
- Added: progress bar for desktop upload.
- Fixed: : WordPress core custom fields issue.

### 5.6.2

- Added: support for remote url without extensions

### 5.6.1

- Added: WP CSRF Protection

### 5.6

- Added: Compatibility for WordPress 5.0

### 5.3.7

- Removed: Registering custom field in mapping section.

### 5.3.6

- Added: Compatibility for WordPress 4.9.8
- Added: Import Post parent import with Post Title
- Fixed: : Post export redirection issue.
- Fixed: : Post slug import in drag and drop method.

### 5.3.5

- Added: Support for serialized data import.
- Added: Compatibility for WordPress 4.9.6
- Improved: Usability and user interface.
- Fixed: : Insertion of hyperlinks in WYSIWYG editor in drag and drop mapping.

### 5.3.4

- Improved: Notifications in mapping
- Fixed: : Export menu issue.

### 5.3.3

- Added: Compatibility for WordPress 4.9
- Moved: User import as add-on.
- Removed: Export

### 5.3.2

- Added: Add-ons support
- Moved: User import as add-on.
- Removed: Export

### 5.3.1

- Modified: Hide the filter based on Condition.
- Fixed: : data loss when page refresh (export).
- Fixed: : warning during Upload.
- Fixed: : user export above 1000 records.
- Fixed: : Custom Field Suite issue.
- Fixed: : Forced quotes issue.

### 5.3

- Added: Prevent loss of mapping data
- Added: Custom field group plugin support
- Added: Maintenance mode
- Added: Inclusion feature
- Updated: Exclude selection as include selection in export module
- Fixed: : User import
- Fixed: : Delimiter issue
- Fixed: : Advance mapping issues
- Fixed: : Post status in Mapping
- Fixed: : Featured image in Mapping
- Fixed: : Post comment in Mapping
- Fixed: : Export page radio button based on plugin activation
- Fixed: : Comment Export
- Fixed: : Text changes

### 5.2

- Added: Advance mapping view with Drag and Drop support.
- Added: Ultimate member plugin support for Users Import.
- Fixed: : Issue with Post format.
- Fixed: : Month order in dashboard charts.
- Added: Latest version support on All in One SEO ********.
- Added: Compatibility for WordPress 4.7.3.

### 5.1.1

- Fixed: : Broken when SCRIPT_DEBUG is true. [Solved](https://wordpress.org/support/topic/broken-when-script_debug-is-true/).
- Fixed: : Issue in duplicate handling to skip the duplicate records.
- Added: Missing font "glyphicons-halflings-regular.woff2".
- Removed: Unwanted console warnings.
- Added: Compatibility for WordPress 4.7.2.

### 5.1

- Added: Language Support for German, French, Italian, Spanish, and Dutch & Russian.
- Added: Restriction to show Admin dashboard widget only for users with Admin role. [Solved](https://wordpress.org/support/topic/admin-dashboard-widget-is-showing-to-all-users)
- Added: Notice to [enable wp-cron](https://www.smackcoders.com/blog/enable-configure-wp-cron.html?utm_source=wp_org&utm_campaign=readme&utm_medium=change_log) to populate the feature images. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
- Added: Warning messages to notify when uploads directory is missing, insufficient permission to access and uploaded file size exceeds your server limits.
- Added: Duplicate handling feature to skip the duplicate records.
- Added: Canonical URL support in All in One SEO data import.
- Improved: CSV export performance.
- Fixed: : All custom fields in WP installation adding to a Post. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
- Fixed: : Mixing up of Custom taxonomies while assigning a term to Post. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
- Fixed: : Adding unwanted data before and after post content. [Solved](https://wordpress.org/support/topic/version-5-03-issues).
- Fixed: : Issue with Post Category & Tags export.
- Fixed: : Missing up of SEO fields in mapping section.
- Fixed: : Issue in exporting All in one SEO fields.
- Fixed: : Issue in assigning page template (wp_page_template).
- Removed: Warnings in migration script.

### 5.0.3

- Added: Support for traditional Chinese characters.
- Fixed: : Author/Editor menu visibility issue
- Fixed: : [Assigning categories to post issue](https://wordpress.org/support/topic/ultimate-csv-importer-v5-adds-all-posts-to-uncategorized/)
- Fixed: : [Import with Chinese character](https://wordpress.org/support/topic/unable-to-import-posts-with-chinese-characters/)

### 5.0.2

- Added: Compatibility from PHP 5.3.

### 5.0.1

- Fixed: : WP Customer Reviews import feature.

### 5.0

- Added: Compatibility for WordPress 4.7 and PHP 7.
- Added: Option to replace imported CSV file value with static and dynamic value.
- Added: Image import from external URL
- Added: Send email to newly imported User with Password Information
- Added: Any Custom Post Type import.
- Added: Post Type import with terms & taxonomies with any depth of parent-child hierarchy.
- Improved: High-speed import with enhanced UI.
- Improved: User role import with capability value or role name in CSV

### 3.11.1

- Fixed: : Browse button disappears in 3.11.0 https://wordpress.org/support/topic/browse-button-disappears-in-3110

### 3.11.0

- Added: Compatibility for WordPress 4.5.3.
- Added: menu_order field Import for Custom Post Type.
- Added: [Support for comma and pipeline separation in multi category & taxonomies import](https://wordpress.org/support/topic/importing-a-taxonomy-field?replies=4).
- Added: Compatibility to export WooCommerce fields with WooCommerce version 2.6.1.
- Updated: Help links of Product page & Live Demo.
- Fixed: : Issues in Taxonomies, Categories & Tags export.
- Fixed: : Issue in export by status filter.

### 3.10.0

- Improvements: Can export any number of records from WordPress site.
- Fixed: : Issue in ACF relationship field export.

### 3.9.4

- Improvements: Duplicate image handling. Option to skip or rename image as image_name-number if image name is same as existing media image name.

### 3.9.3

- Added: Compatibility for WordPress 4.5
- Fixed: : Environment issue with custom port id in MAMP.

### 3.9.2

- Modified: CSVParser Engine with SmackCSVParser, a high-speed robust parser.

### 3.9.1

- Added: Post parent now supports for post title and post name.
- Fixed: : jQuery conflicts.

### 3.9

- Added: PHP 7 compatibility.
- Added: Support for all postdate formats.
- Fixed: : Featured image and Inline image naming issues.
- Fixed: : Auto mapping issues in Custom Fields and SEO Fields.

### 3.8.8

- Added: Localize script for multi-language support.
- Added: WordPress 4.4.1 compatibility.
- Improved: Code cleanups with WordPress standards.
- Fixed: : Vulnerability security issue.
- Fixed: : Export issue.
- Fixed: : Custom Taxonomy import issue.
- Fixed: : User mail notification on new user imports.
- Fixed: : Category & Tag import issue in eShop module.
- Removed: Mod security check.

### 3.8.6

- Added: Compatibility for WordPress 4.4.
- Modified: Support page UI.
- Fixed: : Postdate issue.
- Fixed: : Custom Post Type listing issue.

### 3.8.5

- Added: Restriction to view the image without password for protected status content.
- Modified: Settings page UI.
- Fixed: : post_status mandatory validation issue.
- Fixed: : SEO Fields mapping issue.
- Fixed: : Known issues in export.
- Fixed: : Mandatory validation issues.
- Fixed: : Console TypeError issue.

### 3.8.4

- Modified: Changed the Dashboard view.

### 3.8.3

- Added : Text domain for language translation system.
- Fixed: : Detect duplicate issue.

### 3.8.2

- Added : Compatibility for WordPress 4.3.1.
- Added : Grouped core custom field in mapping section.
- Added : Image import with spaces in image name.
- Fixed: : Module entry count in dashboard issue.
- Fixed: : Duplication of image in media gallery.

### 3.8.1

- Added : Compatibility for WordPress 4.2.3 and 4.2.4.
- Added : Export by specific date and author option in comments.
- Fixed: : warnings triggered in console.
- Fixed: : XSS vulnerability.
- Removed : ../../../../../../wp-load.php and replaced with WordPress Ajax call.
- Removed : Direct usage of wp-content.

### 3.8

- Added : Multi language support (fr_FR,es_ES,nl_NL).
- Added : Inline image handling with short code along with image attributes.
- Added: Any delimiter support for CSV export.
- Fixed: : Warnings and bugs Fixes

### 3.7.4

- Added : WordPress 4.2.2 compatibility.
- Fixed: : Allow Editor/Author to import.(Multisite also).

### 3.7.3

- Fixed: : Vulnerability security issue.

### 3.7.2

- Added: WordPress 4.2 and 4.2.1 compatibility.
- Fixed: : Blank page issue conflicts.

### 3.7.1

- Added: Security fix for curl.
- Added: Security fix for session status.

### 3.7

- Fixed: : Featured image hot link issue.

### 3.6.78

- Added: Hot security fix in readfile.php.

### 3.6.77

- Added: WordPress 4.1.1 compatibility.
- Improved: Inline image import feature.
- Added recursive method to assign the image.
- Fixed: : Featured image naming issue. [Solved](https://wordpress.org/support/topic/problem-in-import-with-the-image-name)
- Removed: Warnings.

### 3.6.76

- Improved: Post Format.
- Fixed: : Export eShop content issue.
- Fixed: : Import with image name issue.
- Fixed: : Groups plugin conflict.

### 3.6.75

- Added: Terminate & Continue option in import.
- Improved: Log section.
- Fixed: : Web View & Admin View issue.
- Fixed: : Security issue in export module.

### 3.6.74

- Fixed: : Security issue.

### 3.6.73

- Added: WordPress 4.0 compatibility.
- Added: https format support for all WP instances.
- Added: Warning to guide user to create uploads directory with writable permissions.
- Improved: Security and performance tab under settings module.
- Fixed: : Featured image-handling issues.
- Fixed: : Multisite compatibility issue.
- Fixed: : All console warnings.
- Removed: Post Content field mandatory option.

### 3.6.72

- Added: Debug mode enable/disable options.
- Modified: Menu order changes.

### 3.6.71

- Fixed: : Minor bugs.

### 3.6.7

- Added: Export features for all missing modules.
- Fixed: : All console warnings and reported logs.

### 3.6.6

- Fixed: : Dashboard chart issue in multisite.
- Modified: UI to improve usability.
- Fixed: : Groups plugin conflicts.

### 3.6.5

- Added: Inline image support with advanced media handling.
- Added: PDO check.

### 3.6.4

- Added: WordPress 4.0 compatibility.
- Added: Advanced export features with filter options.
- Improved: Advanced log section.
- Fixed: : jQuery issues.

### 3.6.3

- Added: eShop import support.
- Added: WordPress 3.9.2 compatibility.
- Fixed: : Conflicts with other plugins.

### 3.6.2

- Fixed: : Hot security issue.

### 3.6.1

- Fixed: : Multi-site support issue.
- Fixed: : Duplicate import issue.
- Fixed: : Security issue.

### 3.6

- Added: Interactive graphs and charts in plugin dashboard.
- Added: Admin dashboard widgets.
- Added: Users and comments export feature.
- Added: Auto delimiter handling.
- Added: Auto mapping feature.
- Added: Allow authors to access import features.

### 3.5.5

- Added: post_format attribute support.
- Added: page_template attribute.
- Added: update_post_meta for duplicate meta issue
- Fixed: : TypeError issue in jQuery.

### 3.5.4

- Added: All in One SEO Pack import support.
- Added: WordPress 3.9.1 compatibility.

### 3.5.3

- Added: Compatibility for WordPress 3.9.
- Added: Export feature for Posts, Page, Custom Post.
- Fixed: : Reported bugs
- Removed: all warnings.

### 3.5.2

- Import posts with author names as numerical apart from User ID
- Added: menu_order attribute import
- Added: Auto image rename option
- Option to cancel any partial import at middle
- Improved image handling even special characters in URL
- Import can handle image URLs without any extensions
- User reported bugs Fixed:

### 3.5.1

- User reported issue fixes
- Activation and other plugin conflict issue solved like Jet pack
- Admin UI freezing issues - screen option, Help links issues Fixed: .
- WYSIWYG editor UI issue Fixed: .

### 3.5.0

- Combined major release version of 3.5 and 3.4
- Improved MVC structure.
- Improved User interface with drag and drop feature.
- Improved: WordPress 3.8.1 compatibility added.
- Module based system allows simplify UI
- Added: Detailed log feature added.
- Added: Support and Useful links added.
- Added: Support made easy now from plugin.

### 3.3.1

- Added: Multisite compatibility except User import.
- Added: Comments, Users modules mandatory fields validation added.
- Improved: Removed unwanted warnings.

### 3.3.0

- Added: WordPress 3.8 compatibility.
- Added: Bulk users with role import feature.
- Added: Comments import feature with relevant post ids.

### 3.2.3

- Added: WordPress 3.7.1 compatibility added.
- Added: Different media path support added.
- Added: Sub folder installations support added.
- Improved: Updated plugin directory path.
- Improved: Removed unwanted warnings.
- Improved: Performance check.

### 3.2.2

- Added: WordPress 3.6.1 compatibility added.
- Added: Mapping UI improved with on select dynamic update feature
- Added: Help content added
- Fixed: : Post slug issue Fixed: and tested for 3.6 and 3.6.1

### 3.2.1

- Improved: Performance improvements on SQL and CSV parsing
- Fixed: : Plugin deactivation issue Fixed: and updated the code.
- Fixed: : Links in the cells makes problems with the "quote"
- Fixed: : Loading content from more than one column
- Fixed: : Custom Post type issues Fixed:

### 3.2.0

- Improved: User interface improvements
- Improved:WordPress 3.6 compatibility added, Much Improved UI.
- Fixed: : Featured image issues Fixed: for WordPress-3.6.

### 3.1.0

- Improved: Much Improved Featured Image feature
- Fixed: : Image URL for featured image issues Fixed:
- Fixed: : PHP 5.4 upgrade fix

### 3.0.0

- Added: Category in numeric are restricted and skipped to Uncategorized
- Added: Protected status password inclusion as {password}.
- Added: Post authors can be User ID or name
- Improved: Much improved workflow
- Improved: Add custom field option improved.
- Improved: Date format handling improved
- Improved: Any Date format supported now
- Improved: Future scheduling and status improved
- Improved: Can apply post status for individual post via CSV itself
- Improved: Featured image handling improved and Fixed: . More improvement are scheduled.
- Improved: Duplicate check options improved for both title and content option.
- Improved: Post author issue Fixed: and improved
- Improved: Wrong user id or name are automatically assigned under admin
- Improved: Multi category and tags improved
- Fixed: : Custom Field mapping and import Fixed:
- Fixed: : Overall Status option improved and issue Fixed:
- Fixed: : Password field Fixed: for Protected
- Fixed: : Status as in CSV option improved and Fixed:

### 2.7.0

- Added: Added more post status options
- Added: Publish, Sticky, Private, Draft and Pending Status for whole import
- Added: Protected status with a common password option added
- Added: "Status as in CSV" to assign status for individual post through CSV as ID or Field Tag
- Added: User ID and User Name support for Post author feature added
- Added: In case of missing or false IDs post assigned to admin as draft
- Added: Add Custom Field Text box auto-filled with CSV header tag.
- Added: Duplicate detection for post content and post title added as options.
- Added: User can choose either one or both to avoid duplicate issues.
- Improved: 6 Standard date format added as drop down to choose.
- Improved: Renamed post_name as post_slug to avoid confusion
- Improved: Mapping Fields
- Improved: Field tags are formatted to support auto mapping option (next milestone)
- Improved: Listed custom fields with prefix as CF: Name for easy identification.
- Fixed: : Date format conflict at import Fixed: .

### 2.6.0

- Fixed: : Major Bug Fixed:
- Fixed: : Added UTF-8 support.
- Fixed: : Fixed: HTML tag conflicts.

### 2.5.0

- Major issues Fixed: and updated to WordPress-3.5.1 compatibility.

### 2.0.1

- Update to WordPress-3.5 compatibility.

### 2.0.0

- WPDEBUG errors Fixed: . CSV import folder changed to WP native uploads folder.

### 1.1.1

- Renamed the mapping field attachment as featured_image and category as post_category.

### 1.1.0

- Added featured image import feature along with post/page/custom post.

### 1.0.2

- Bug Fixed: to recognize the trimmed trailing space in the CSV file
- Added validation for the duplicate field mapping.

### 1.0.1

- Added features to import multiple tags and categories with different delimiters.

### 1.0.0

- Initial release version. Tested and found works well without any issues.

## Upgrade Notice
### 6.5.3 
- Added: Upgrade now for latest updates.

### 6.5.2 
- Added: Upgrade now for latest updates.

### 6.5.1

- Added: Upgrade now for latest updates.
### 6.5

- Added: Upgrade now for latest updates.
### 6.4.4

- Added: Upgrade now for latest updates.
### 6.4.3

- Added: Upgrade now for latest updates.

### 6.4.2

- Added: Upgrade now for latest updates.

### 6.4.1

- Added: Upgrade now for latest updates.

### 6.4 

- Added: Upgrade now for latest updates.

### 6.3 

- Added: Upgrade now for latest updates.

### 6.2.9 

- Added: Upgrade now for latest updates.

### 6.2.8 

- Added: Upgrade now for latest updates.

### 6.2.7 

- Added: Upgrade now for latest updates.

### 6.2.6 

- Added: Upgrade now for latest updates.

### 6.2.5 

- Added: Upgrade now for latest updates.

### 6.2.4 

- Added: Upgrade now for latest updates.

### 6.2.3 

- Added: Upgrade now for latest updates.

### 6.2.2

- Added: Upgrade now for latest updates.

### 6.2.1

- Added: Upgrade now for latest updates.

### 6.2

- Added: Upgrade now for latest updates.

### 6.1.9

- Added: Upgrade now for latest updates.

### 6.1.8

- Added: Upgrade now for latest updates.

### 6.1.7

- Added: Upgrade now for latest updates.

### 6.1.6

- Added: Upgrade now for latest updates.

### 6.1.5

- Added: Upgrade now for latest updates.

### 6.1.4

- Added: Upgrade now for latest updates.

### 6.1.3

- Added: Upgrade now for latest updates.

### 6.1.2

- Added: Upgrade now for latest updates.

### 6.1.1

- Added: Upgrade now for latest updates.

### 6.1

- Added: Upgrade now for newly added features.

### 6.0.9

- Added: Upgrade now for latest updates.

### 6.0.8

- Added: Upgrade now for newly added features.

### 6.0.7

- Added: Upgrade now for newly added features

### 6.0.6

- Upgrade now for existing image import.

### 6.0.5

- Upgrade now for xml import and bug fixes

### 6.0.4

- Important update with issue fixes.

### 6.0.3

- Upgrade now for much improved and stable release

### 6.0.2

- Upgrade now added feature/improvements and known issue fixes

### 6.0.1

- Upgrade now for more improvements and known issue fixes

### 6.0

- Upgrade now for overall improvements changes.

### 5.6.2

- Update for improve image import.

### 5.6.1

- Important update for security fixes.

### 5.6

- Upgrade now for WordPress 5.0 compatibility

### 5.3.7

- Upgrade now to get latest version of Ultimate CSV Importer

### 5.3.6

- Upgrade now to get Post parent import with Post title and compatibility for WordPress 4.9.8

### 5.3.5

- Upgrade now to get support for serialized data import and compatibility for WordPress 4.9.6

### 5.3.4

- Upgrade now for Export issue fix.

### 5.3.3

- Upgrade now for WordPress 4.9 compatibility.

### 5.3.2

- Upgrade now for getting addons support

### 5.3.1

- Upgrade now to get the fix for Export,CFS and WordPress 4.8.1 compatibility.

### 5.3

- Major improvements and fix updates, verify change log for upgrade.

### 5.2

- Upgrade now to get enhanced User Interface and support for latest version of All in one SEO. ********.

### 5.1.1

- Upgrade now to get the fix for duplicate handling feature.

### 5.1

- Upgrade now for six language support, duplicate handling and more.

### 5.0.3

- Upgrade now for Chinese character support and a fix for category import.

### 5.0.2

- Upgrade now to get the fix for PHP 5.3 compatibility.

### 5.0.1

- Upgrade now to get the fix for WP Customer Review add-on support.

### 5.0

- Upgrade now for high-speed import, WP Customer Review add-on support, WordPress 4.7 Compatibility and much more.

### 3.11.0

- Upgrade now for WordPress 4.5.3 and WooCommerce 4.3.1 compatibility and other minor enhancements.

### 3.10.0

- Upgrade now to export ACF relationship field and improved export features.

### 3.9.4

- Upgrade now for duplicate image handling feature.

### 3.9.3

- Upgrade now to get support for custom port id in MAMP environment and WordPress 4.5 compatibility.

### 3.9.2

- Upgrade now to get high-speed robust parser.

### 3.9.1

- Upgrade now for import post parent with post name and post title.

### 3.9

- Upgrade now for PHP 7 and WordPress 4.4.2 compatibility.

### 3.8.8

- Upgrade now for security fix and WordPress 4.1.1 compatibility with minor bug fixes.

### 3.8.6

- Upgrade now for import the default CPTs.

### 3.8.5

- Upgrade now for known bug fixes.

### 3.8.4

- Upgrade now for new dashboard view.

### 3.8.3

- Upgrade now for improvements in multi-language translation.

### 3.8.2

- Upgrade now for more improvements.

### 3.8.1

- Upgrade now for ABSPATH used in all files and also Fixed: all the warnings,bugs.

### 3.8

- Upgrade now for Multi language support and bug fixes

### 3.7.4

- Upgrade now for allow author/editor to import fix.

### 3.7.3

- Upgrade now for Vulnerability fix.

### 3.7.2

- Important Upgrade for WordPress 4.2 and above

### 3.7.1

- Upgrade now for security fix.

### 3.7

- Upgrade now for minor bug fixes.

### 3.6.78

- Upgrade now for security fix.

### 3.6.77

- Upgrade now for WordPress 4.1.1 compatibility and minor bug fixes.

### 3.6.76

- Upgrade now for more bug fixes.

### 3.6.75

- Upgrade now for improved experience.

### 3.6.74

- Upgrade now for security fix.

### 3.6.73

- Upgrade now for WordPress 4.0 compatibility and minor bug fixes.

### 3.6.72

- Upgrade to enable/disable debug mode in settings.

### 3.6.71

- Upgrade now for complete export features as in Pro with bug fix.

### 3.6.7

- Upgrade now for complete export features as in Pro.

### 3.6.6

- Upgrade now for improved experience.

### 3.6.5

- Upgrade Now for advanced media handling for post in line images

### 3.6.4

- Upgrade Now for filtered export and improved log features.

### 3.6.3

- Upgrade now for eShop product import feature.

### 3.6.2

- Important Security Update

### 3.6.1

- Important issue fixes update

### 3.6

- Must upgrade with major values added features

### 3.5.5

- Upgrade for minor bug fix, conflicts and new attribute support.

### 3.5.4

- Upgrade now for All in SEO Pack import support.

### 3.5.3

- Upgrade to add export feature and fix bug.

### 3.5.2

- Upgrade for bug free version and improved image handling

### 3.5.1

- Must upgrade now for 3.5 bug fixes

### 3.5.0

- Upgrade now for major release 3.5. Note - Old version files need to be deleted.

### 3.3.1

- Upgrade now for multisite compatibility.

### 3.3.0

- Upgrade now for WP 3.8 compatibility and added bulk user, comments feature.

### 3.2.3

- WordPress 3.7.1 compatibility and minor bug fixes

### 3.2.2

- WordPress 3.6.1 compatibility, bug fix and UI improvements

### 3.2.1

- Performance improvements on SQL and CSV parsing

### 3.2.0

- Compatibility for 3.6 and improved featured image.

### 3.1.0

- Improved Featured image and URL handling

### 3.0.0

- Major performance improvements and issue fixes.

### 2.7.0

- Major improvements and feature changes.

### 2.6.0

- Bug Fixed: and should upgrade.

### 2.5.0

- Duplicate detection added.
- Added more information in success message.
- Import memory issues solved.

### 2.0.1

- WordPress-3.5 compatibility.

### 2.0.0

- Major Bug fix and need to upgraded.
- WPDEBUG error Fixed: .
- CSV import folder changed to WP native uploads folder.

### 1.1.1

- Minor correction and fix applied.

### 1.1.0

- Major feature added.

### 1.0.2

- Bug fixes along with new features. Need to upgraded immediately.

### 1.0.1

- Added features to import multiple tags and categories with different delimiters.

### 1.0.0

- Initial release of plugin.
