<?php declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON>\Parser;

use Php<PERSON><PERSON><PERSON>\Error;
use Php<PERSON>arser\Modifiers;
use Php<PERSON>arser\Node;
use PhpParser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use Php<PERSON><PERSON>er\Node\Scalar;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar file grammar/php.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php8 extends \PhpParser\ParserAbstract
{
    public const YYERRTOK = 256;
    public const T_THROW = 257;
    public const T_INCLUDE = 258;
    public const T_INCLUDE_ONCE = 259;
    public const T_EVAL = 260;
    public const T_REQUIRE = 261;
    public const T_REQUIRE_ONCE = 262;
    public const T_LOGICAL_OR = 263;
    public const T_LOGICAL_XOR = 264;
    public const T_LOGICAL_AND = 265;
    public const T_PRINT = 266;
    public const T_YIELD = 267;
    public const T_DOUBLE_ARROW = 268;
    public const T_YIELD_FROM = 269;
    public const T_PLUS_EQUAL = 270;
    public const T_MINUS_EQUAL = 271;
    public const T_MUL_EQUAL = 272;
    public const T_DIV_EQUAL = 273;
    public const T_CONCAT_EQUAL = 274;
    public const T_MOD_EQUAL = 275;
    public const T_AND_EQUAL = 276;
    public const T_OR_EQUAL = 277;
    public const T_XOR_EQUAL = 278;
    public const T_SL_EQUAL = 279;
    public const T_SR_EQUAL = 280;
    public const T_POW_EQUAL = 281;
    public const T_COALESCE_EQUAL = 282;
    public const T_COALESCE = 283;
    public const T_BOOLEAN_OR = 284;
    public const T_BOOLEAN_AND = 285;
    public const T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG = 286;
    public const T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG = 287;
    public const T_IS_EQUAL = 288;
    public const T_IS_NOT_EQUAL = 289;
    public const T_IS_IDENTICAL = 290;
    public const T_IS_NOT_IDENTICAL = 291;
    public const T_SPACESHIP = 292;
    public const T_IS_SMALLER_OR_EQUAL = 293;
    public const T_IS_GREATER_OR_EQUAL = 294;
    public const T_SL = 295;
    public const T_SR = 296;
    public const T_INSTANCEOF = 297;
    public const T_INC = 298;
    public const T_DEC = 299;
    public const T_INT_CAST = 300;
    public const T_DOUBLE_CAST = 301;
    public const T_STRING_CAST = 302;
    public const T_ARRAY_CAST = 303;
    public const T_OBJECT_CAST = 304;
    public const T_BOOL_CAST = 305;
    public const T_UNSET_CAST = 306;
    public const T_POW = 307;
    public const T_NEW = 308;
    public const T_CLONE = 309;
    public const T_EXIT = 310;
    public const T_IF = 311;
    public const T_ELSEIF = 312;
    public const T_ELSE = 313;
    public const T_ENDIF = 314;
    public const T_LNUMBER = 315;
    public const T_DNUMBER = 316;
    public const T_STRING = 317;
    public const T_STRING_VARNAME = 318;
    public const T_VARIABLE = 319;
    public const T_NUM_STRING = 320;
    public const T_INLINE_HTML = 321;
    public const T_ENCAPSED_AND_WHITESPACE = 322;
    public const T_CONSTANT_ENCAPSED_STRING = 323;
    public const T_ECHO = 324;
    public const T_DO = 325;
    public const T_WHILE = 326;
    public const T_ENDWHILE = 327;
    public const T_FOR = 328;
    public const T_ENDFOR = 329;
    public const T_FOREACH = 330;
    public const T_ENDFOREACH = 331;
    public const T_DECLARE = 332;
    public const T_ENDDECLARE = 333;
    public const T_AS = 334;
    public const T_SWITCH = 335;
    public const T_MATCH = 336;
    public const T_ENDSWITCH = 337;
    public const T_CASE = 338;
    public const T_DEFAULT = 339;
    public const T_BREAK = 340;
    public const T_CONTINUE = 341;
    public const T_GOTO = 342;
    public const T_FUNCTION = 343;
    public const T_FN = 344;
    public const T_CONST = 345;
    public const T_RETURN = 346;
    public const T_TRY = 347;
    public const T_CATCH = 348;
    public const T_FINALLY = 349;
    public const T_USE = 350;
    public const T_INSTEADOF = 351;
    public const T_GLOBAL = 352;
    public const T_STATIC = 353;
    public const T_ABSTRACT = 354;
    public const T_FINAL = 355;
    public const T_PRIVATE = 356;
    public const T_PROTECTED = 357;
    public const T_PUBLIC = 358;
    public const T_READONLY = 359;
    public const T_VAR = 360;
    public const T_UNSET = 361;
    public const T_ISSET = 362;
    public const T_EMPTY = 363;
    public const T_HALT_COMPILER = 364;
    public const T_CLASS = 365;
    public const T_TRAIT = 366;
    public const T_INTERFACE = 367;
    public const T_ENUM = 368;
    public const T_EXTENDS = 369;
    public const T_IMPLEMENTS = 370;
    public const T_OBJECT_OPERATOR = 371;
    public const T_NULLSAFE_OBJECT_OPERATOR = 372;
    public const T_LIST = 373;
    public const T_ARRAY = 374;
    public const T_CALLABLE = 375;
    public const T_CLASS_C = 376;
    public const T_TRAIT_C = 377;
    public const T_METHOD_C = 378;
    public const T_FUNC_C = 379;
    public const T_LINE = 380;
    public const T_FILE = 381;
    public const T_START_HEREDOC = 382;
    public const T_END_HEREDOC = 383;
    public const T_DOLLAR_OPEN_CURLY_BRACES = 384;
    public const T_CURLY_OPEN = 385;
    public const T_PAAMAYIM_NEKUDOTAYIM = 386;
    public const T_NAMESPACE = 387;
    public const T_NS_C = 388;
    public const T_DIR = 389;
    public const T_NS_SEPARATOR = 390;
    public const T_ELLIPSIS = 391;
    public const T_NAME_FULLY_QUALIFIED = 392;
    public const T_NAME_QUALIFIED = 393;
    public const T_NAME_RELATIVE = 394;
    public const T_ATTRIBUTE = 395;

    protected int $tokenToSymbolMapSize = 396;
    protected int $actionTableSize = 1257;
    protected int $gotoTableSize = 657;

    protected int $invalidSymbol = 168;
    protected int $errorSymbol = 1;
    protected int $defaultAction = -32766;
    protected int $unexpectedTokenRule = 32767;

    protected int $YY2TBLSTATE = 435;
    protected int $numNonLeafStates = 739;

    protected array $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "'.'",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'('",
        "')'",
        "'{'",
        "'}'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected array $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  166,  168,  167,   55,  168,  168,
          161,  162,   53,   51,    8,   52,   48,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  159,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  160,   36,  168,  165,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  163,   35,  164,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   49,   50,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158
    );

    protected array $action = array(
          133,  134,  135,  582,  136,  137,    0,  751,  752,  753,
          138,   38,  327,-32766,-32766,-32766,-32766,-32766,-32766,  837,
          826,-32767,-32767,-32767,-32767,  102,  103,  104, 1112, 1113,
         1114, 1111, 1110, 1109, 1115,  745,  744,-32766, 1027,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767, 1245,-32766,-32766, 1322,  754, 1112, 1113, 1114, 1111,
         1110, 1109, 1115,  459,  460,  461,    2,  990, 1306,  265,
          139,  404,  758,  759,  760,  761,  467,  468,  429,  835,
          606,  -16, 1341,   23,  292,  815,  762,  763,  764,  765,
          766,  767,  768,  769,  770,  771,  791,  583,  792,  793,
          794,  795,  783,  784,  345,  346,  786,  787,  772,  773,
          774,  776,  777,  778,  356,  818,  819,  820,  821,  822,
          584,  779,  780,  585,  586,  941,  803,  801,  802,  814,
          798,  799,  835,  826,  587,  588,  797,  589,  590,  591,
          592,  593,  594, -328,   36,  251,   35, -194,  800,  595,
          596, -193,  140,  -85,  133,  134,  135,  582,  136,  137,
         1060,  751,  752,  753,  138,   38,  129, -110, -110, -585,
        -32766, -585, -110,-32766,-32766,-32766,  241,  836, -110,  145,
          959,  960,-32766,-32766,-32766,  961, -594,-32766,  482,  745,
          744,  955, 1036, -594,-32766,  991,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32766,-32766,  299,  754,
          831,   75,-32766,-32766,-32766,  291,  142,  326,  242,  -85,
          326,  382,  381,  265,  139,  404,  758,  759,  760,  761,
           82,  423,  429,-32766,  326,-32766,-32766,-32766,-32766,  815,
          762,  763,  764,  765,  766,  767,  768,  769,  770,  771,
          791,  583,  792,  793,  794,  795,  783,  784,  345,  346,
          786,  787,  772,  773,  774,  776,  777,  778,  356,  818,
          819,  820,  821,  822,  584,  779,  780,  585,  586,  254,
          803,  801,  802,  814,  798,  799,  832,  725,  587,  588,
          797,  589,  590,  591,  592,  593,  594, -328,   83,   84,
           85, -194,  800,  595,  596, -193,  149,  775,  746,  747,
          748,  749,  750,  151,  751,  752,  753,  788,  789,   37,
          483,   86,   87,   88,   89,   90,   91,   92,   93,   94,
           95,   96,   97,   98,   99,  100,  101,  102,  103,  104,
          105,  106,  107,  108,  109, -594,  274, -594,-32766,-32766,
        -32766,-32766,-32766,-32766,  310, 1089,  127,  312,  110,  737,
         1326,   21,  754,-32766,-32766,-32766, -272, 1325,-32766,-32766,
         1088,-32766,-32766,-32766,-32766,-32766,  755,  756,  757,  758,
          759,  760,  761, 1104,-32766,  824,-32766,-32766, -545,  429,
         1036,  323,  815,  762,  763,  764,  765,  766,  767,  768,
          769,  770,  771,  791,  813,  792,  793,  794,  795,  783,
          784,  785,  812,  786,  787,  772,  773,  774,  776,  777,
          778,  817,  818,  819,  820,  821,  822,  823,  779,  780,
          781,  782, 1033,  803,  801,  802,  814,  798,  799,  745,
          744,  790,  796,  797,  804,  805,  807,  806,  808,  809,
          152,-32766, -545, -545, 1036,  800,  811,  810,   50,   51,
           52,  513,   53,   54, 1240, 1239, 1241, -545,   55,   56,
         -110,   57,-32766, 1090,  920, -110,  556, -110,  292, -551,
          339, -545,  306,  103,  104, -110, -110, -110, -110, -110,
         -110, -110, -110,  105,  106,  107,  108,  109, 1245,  274,
          380,  381, -591, -367,  715, -367,  340,   58,   59, -591,
          423,  110,   60,  370,   61,  248,  249,   62,   63,   64,
           65,   66,   67,   68,   69, -544,   28,  267,   70,  445,
          514,-32766,  374, -342, 1272, 1273,  515, 1278,  835,  862,
          389,  863, 1270,   42,   25,  516,  943,  517,  943,  518,
          920,  519,  299, 1036,  520,  521, 1266,  910,  441,   44,
           45,  446,  377,  376,-32766,   46,  522, 1023, 1022, 1021,
         1024,  368,  338,  391, 1238,    7,  291,  442, 1231,  835,
          524,  525,  526,  443, 1245,  357, 1036,  362,  834, -544,
         -544,  154,  528,  529,  444, 1259, 1260, 1261, 1262, 1256,
         1257,  298,-32766,-32766, -544, -548, 1059, 1263, 1258,  291,
         1236, 1240, 1239, 1241,  299,  841, -550,   71, -544,  656,
           26,  321,  322,  326, -153, -153, -153,  920,  612,  675,
          676, 1035,  922,  910,-32766,  286,  710,  835,  155, -153,
          828, -153,  862, -153,  863, -153,  150,  407,  156, 1240,
         1239, 1241,-32766,-32766,-32766,  375, 1351,  716,   75, 1352,
          158, -591,   33, -591,  326,  835,  959,  960,  -78, -548,
         -548,  523,  920,-32766,  378,  379,  896,  955, -110, -110,
         -110,   32,  111,  112,  113,  114,  115,  116,  117,  118,
          119,  120,  121,  122,  123,  745,  744,  -58, -548,  -57,
         -110, -110,  717,  745,  744, -110,  383,  384,  922, 1033,
          910, -110,  710, -153,  647,  648,  830,  124,  141,  125,
        -32766, 1033,  326,  712, 1150, 1152,   48,  130,  131,  144,
          159, 1036,-32766,  160,  161, -543,   28,  162, 1238,  920,
          163,  299,  920, 1036,   75,-32766,-32766,-32766,  835,-32766,
          326,-32766, 1270,-32766,  282,  910,-32766,  -87,  -84,  -78,
          -73,-32766,-32766,-32766,   -4,  920,  282,-32766,-32766,  720,
          -72,  -71,  727,-32766,  420,  -70,  -69,  -68,  -67,  -66,
          287,  286,-32766,  -65,  -46,  922,  745,  744, 1231,  710,
          300,  301, -546,  -18,  148, -302,  273,  283,  726, -543,
         -543,  729,  528,  529,  920, 1259, 1260, 1261, 1262, 1256,
         1257,  919,   74,  147, -543,  288,  293, 1263, 1258,  126,
         -298,  280,  910,-32766,  281,  910,  284,   73, -543, 1238,
          976,  690,  322,  326,  710,  285,-32766,-32766,-32766,  332,
        -32766,  274,-32766,  294,-32766,  937,  110,-32766,  910,  685,
          835, -543,-32766,-32766,-32766,  826, -546, -546,-32766,-32766,
          146,-32766,  -50,  701,-32766,  420,  703,  691,   20, 1119,
          375, -546,  436,-32766,  645, 1353, 1277,  297,  657,-32766,
         1279,  959,  960,  561,  956, -546,  523,  910,  692,  693,
          678,  527,  955, -110, -110, -110,  132,  922,  662,  663,
          922,  710,  464, -508,  710,-32766, 1240, 1239, 1241,  493,
          679, 1238,  282,  939,   10, -543, -543,   40,-32766,-32766,
        -32766,  731,-32766,  922,-32766,  307,-32766,  710,   -4,-32766,
         -543,  305,   41,  304,-32766,-32766,-32766,    0,    0,-32766,
        -32766,-32766,  920,    0, -543, 1238,-32766,  420,  311,    0,
          567,  299,-32766,-32766,-32766,-32766,-32766, -498,-32766,  897,
        -32766,    0,  922,-32766,    8,    0,  710,   24,-32766,-32766,
        -32766,-32766,  372,  610,-32766,-32766,  834, 1238,  734, -275,
        -32766,  420,  920,  735,-32766,-32766,-32766,  854,-32766,-32766,
        -32766,  901,-32766, 1000,  977,-32766,   49,  984,  974,  488,
        -32766,-32766,-32766,-32766,  985,  899,-32766,-32766,  972, 1238,
          574, 1093,-32766,  420, 1096, 1097,-32766,-32766,-32766, 1094,
        -32766,-32766,-32766, 1095,-32766,  910, 1101,-32766, 1267,  846,
         1292, 1310,-32766,-32766,-32766, 1344,  650,   34,-32766,-32766,
         -579, -250, -250, -250,-32766,  420, -578,  375, -577, -551,
           28,  267, -550,-32766, -549, -492,    1,   29,  959,  960,
          302,  303,  835,  523,   30,  910, 1270,   39,  896,  955,
         -110, -110, -110,   43,   47,  373,   72,   76,   77,   78,
           79, -249, -249, -249,   80,   81,  143,  375,  153,  128,
         -273,  157,  247,  328,  357,  358,  359,  360,  959,  960,
          922,  361, 1231,  523,  710, -250,  362,  363,  896,  955,
         -110, -110, -110,  364,  365,  366,  367,  529,   28, 1259,
         1260, 1261, 1262, 1256, 1257,  369,  437,  555, 1207, -272,
          835, 1263, 1258,   13, 1270,   14,-32766,   15,   16,   18,
          922,   73, 1238, 1348,  710, -249,  322,  326,  406,-32766,
        -32766,-32766,  484,-32766,  485,-32766,  492,-32766,  495,  496,
        -32766,  497,  498,  502,  503,-32766,-32766,-32766,  504,  511,
         1231,-32766,-32766,  572,  696, 1249, 1190,-32766,  420, 1268,
         1062, 1061, 1042, 1226, 1038,  529,-32766, 1259, 1260, 1261,
         1262, 1256, 1257, -277, -102,   12,   17,   27,  296, 1263,
         1258,  405,  603,  607,  636,  702, 1194, 1244, 1191,   73,
          320, 1323,    0,  371,  322,  326,  711,  714,  718,  719,
          721,  722,  723,  724,  728,    0,  713,    0, 1350,  857,
          856,  865,  949,  992,  864, 1349,  948,  946,  947,  950,
         1222,  930,  940,  928,  982,  983,  634, 1347, 1304, 1293,
         1311, 1320,    0,    0, 1271,    0,  326
    );

    protected array $actionCheck = array(
            2,    3,    4,    5,    6,    7,    0,    9,   10,   11,
           12,   13,   70,    9,   10,   11,    9,   10,   11,    1,
           80,   44,   45,   46,   47,   48,   49,   50,  116,  117,
          118,  119,  120,  121,  122,   37,   38,   30,    1,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,    1,    9,   10,    1,   57,  116,  117,  118,  119,
          120,  121,  122,  129,  130,  131,    8,   31,    1,   71,
           72,   73,   74,   75,   76,   77,  134,  135,   80,   82,
            1,   31,   85,    8,   30,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,    1,  128,  129,  130,  131,
          132,  133,   82,   80,  136,  137,  138,  139,  140,  141,
          142,  143,  144,    8,  147,  148,    8,    8,  150,  151,
          152,    8,  154,   31,    2,    3,    4,    5,    6,    7,
          162,    9,   10,   11,   12,   13,    8,  117,  118,  160,
          116,  162,  122,    9,   10,   11,   97,  159,  128,    8,
          117,  118,    9,   10,   11,  122,    1,  137,   31,   37,
           38,  128,  138,    8,   30,  159,   32,   33,   34,   35,
           36,   37,   38,   30,    9,   32,   33,   34,  158,   57,
           80,  161,    9,   10,   11,  161,  163,  167,   14,   97,
          167,  106,  107,   71,   72,   73,   74,   75,   76,   77,
          163,  116,   80,   30,  167,   32,   33,   34,   35,   87,
           88,   89,   90,   91,   92,   93,   94,   95,   96,   97,
           98,   99,  100,  101,  102,  103,  104,  105,  106,  107,
          108,  109,  110,  111,  112,  113,  114,  115,  116,  117,
          118,  119,  120,  121,  122,  123,  124,  125,  126,    8,
          128,  129,  130,  131,  132,  133,  156,  163,  136,  137,
          138,  139,  140,  141,  142,  143,  144,  162,    9,   10,
           11,  162,  150,  151,  152,  162,  154,    2,    3,    4,
            5,    6,    7,   14,    9,   10,   11,   12,   13,   30,
          163,   32,   33,   34,   35,   36,   37,   38,   39,   40,
           41,   42,   43,   44,   45,   46,   47,   48,   49,   50,
           51,   52,   53,   54,   55,  160,   57,  162,    9,   10,
           11,    9,   10,   11,    8,  159,   14,    8,   69,  163,
            1,  101,   57,    9,   10,   11,  162,    8,  116,   30,
            1,   32,   33,   34,   35,   36,   71,   72,   73,   74,
           75,   76,   77,  123,   30,   80,   32,   33,   70,   80,
          138,    8,   87,   88,   89,   90,   91,   92,   93,   94,
           95,   96,   97,   98,   99,  100,  101,  102,  103,  104,
          105,  106,  107,  108,  109,  110,  111,  112,  113,  114,
          115,  116,  117,  118,  119,  120,  121,  122,  123,  124,
          125,  126,  116,  128,  129,  130,  131,  132,  133,   37,
           38,  136,  137,  138,  139,  140,  141,  142,  143,  144,
           14,  116,  134,  135,  138,  150,  151,  152,    2,    3,
            4,    5,    6,    7,  155,  156,  157,  149,   12,   13,
          101,   15,  137,  164,    1,  106,   85,  108,   30,  161,
            8,  163,  113,   49,   50,  116,  117,  118,  119,  120,
          121,  122,  123,   51,   52,   53,   54,   55,    1,   57,
          106,  107,    1,  106,   31,  108,    8,   51,   52,    8,
          116,   69,   56,    8,   58,   59,   60,   61,   62,   63,
           64,   65,   66,   67,   68,   70,   70,   71,   72,   73,
           74,  116,    8,  164,   78,   79,   80,  146,   82,  106,
            8,  108,   86,   87,   88,   89,  122,   91,  122,   93,
            1,   95,  158,  138,   98,   99,    1,   84,    8,  103,
          104,  105,  106,  107,  116,  109,  110,  119,  120,  121,
          122,  115,  116,  106,   80,  108,  161,    8,  122,   82,
          124,  125,  126,    8,    1,  161,  138,  161,  155,  134,
          135,   14,  136,  137,    8,  139,  140,  141,  142,  143,
          144,  145,   51,   52,  149,   70,    1,  151,  152,  161,
          116,  155,  156,  157,  158,    8,  161,  161,  163,   75,
           76,  165,  166,  167,   75,   76,   77,    1,   52,   75,
           76,  137,  159,   84,  137,   30,  163,   82,   14,   90,
           80,   92,  106,   94,  108,   96,  101,  102,   14,  155,
          156,  157,    9,   10,   11,  106,   80,   31,  161,   83,
           14,  160,   14,  162,  167,   82,  117,  118,   16,  134,
          135,  122,    1,   30,  106,  107,  127,  128,  129,  130,
          131,   16,   17,   18,   19,   20,   21,   22,   23,   24,
           25,   26,   27,   28,   29,   37,   38,   16,  163,   16,
          117,  118,   31,   37,   38,  122,  106,  107,  159,  116,
           84,  128,  163,  164,  111,  112,  156,   16,  163,   16,
          137,  116,  167,  163,   59,   60,   70,   16,   16,   16,
           16,  138,   74,   16,   16,   70,   70,   16,   80,    1,
           16,  158,    1,  138,  161,   87,   88,   89,   82,   91,
          167,   93,   86,   95,  161,   84,   98,   31,   31,   31,
           31,  103,  104,  105,    0,    1,  161,  109,  110,   31,
           31,   31,   31,  115,  116,   31,   31,   31,   31,   31,
           37,   30,  124,   31,   31,  159,   37,   38,  122,  163,
          134,  135,   70,   31,   31,   35,   31,   31,   31,  134,
          135,   31,  136,  137,    1,  139,  140,  141,  142,  143,
          144,   31,  154,   31,  149,   37,   37,  151,  152,  163,
           35,   35,   84,   74,   35,   84,   35,  161,  163,   80,
          159,   80,  166,  167,  163,   35,   87,   88,   89,   35,
           91,   57,   93,   37,   95,   38,   69,   98,   84,   77,
           82,   70,  103,  104,  105,   80,  134,  135,  109,  110,
           70,   85,   31,   80,  115,  116,   92,  116,   97,   82,
          106,  149,  108,  124,  113,   83,  146,  113,   90,  137,
          146,  117,  118,   89,  128,  163,  122,   84,  137,  138,
           94,  127,  128,  129,  130,  131,   31,  159,   96,  100,
          159,  163,   97,  149,  163,   74,  155,  156,  157,   97,
          100,   80,  161,  154,  150,  134,  135,  159,   87,   88,
           89,  164,   91,  159,   93,  114,   95,  163,  164,   98,
          149,  133,  159,  132,  103,  104,  105,   -1,   -1,   74,
          109,  110,    1,   -1,  163,   80,  115,  116,  132,   -1,
          153,  158,   87,   88,   89,  124,   91,  149,   93,  164,
           95,   -1,  159,   98,  149,   -1,  163,  149,  103,  104,
          105,   74,  149,  153,  109,  110,  155,   80,  159,  162,
          115,  116,    1,  159,   87,   88,   89,  159,   91,  124,
           93,  159,   95,  159,  159,   98,   70,  159,  159,  102,
          103,  104,  105,   74,  159,  159,  109,  110,  159,   80,
           81,  159,  115,  116,  159,  159,   87,   88,   89,  159,
           91,  124,   93,  159,   95,   84,  159,   98,  160,  160,
          160,  160,  103,  104,  105,  160,  160,  163,  109,  110,
          161,  100,  101,  102,  115,  116,  161,  106,  161,  161,
           70,   71,  161,  124,  161,  161,  161,  161,  117,  118,
          134,  135,   82,  122,  161,   84,   86,  161,  127,  128,
          129,  130,  131,  161,  161,  149,  161,  161,  161,  161,
          161,  100,  101,  102,  161,  161,  161,  106,  161,  163,
          162,  161,  161,  161,  161,  161,  161,  161,  117,  118,
          159,  161,  122,  122,  163,  164,  161,  161,  127,  128,
          129,  130,  131,  161,  161,  161,  161,  137,   70,  139,
          140,  141,  142,  143,  144,  161,  161,  161,  165,  162,
           82,  151,  152,  162,   86,  162,   74,  162,  162,  162,
          159,  161,   80,  164,  163,  164,  166,  167,  162,   87,
           88,   89,  162,   91,  162,   93,  162,   95,  162,  162,
           98,  162,  162,  162,  162,  103,  104,  105,  162,  162,
          122,  109,  110,  162,  162,  162,  162,  115,  116,  162,
          162,  162,  162,  162,  162,  137,  124,  139,  140,  141,
          142,  143,  144,  162,  162,  162,  162,  162,  162,  151,
          152,  162,  162,  162,  162,  162,  162,  162,  162,  161,
          163,  162,   -1,  163,  166,  167,  163,  163,  163,  163,
          163,  163,  163,  163,  163,   -1,  163,   -1,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,   -1,   -1,  166,   -1,  167
    );

    protected array $actionBase = array(
            0,   -2,  152,  549,  764,  941,  981,  751,  555,  309,
          560,  864,  626,  738,  738,  741,  738,  473,  671,  783,
          -60,  305,  305,  783,  305,  803,  803,  803,  658,  658,
          658,  658,  749,  749,  897,  897,  929,  865,  831, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062, 1062,
         1062, 1062, 1062, 1062,   18,   36,   79,  648, 1036, 1044,
         1040, 1045, 1034, 1033, 1039, 1041, 1046, 1083, 1084,  782,
         1085, 1086, 1082, 1087, 1042,  876, 1035, 1043,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  195,  342,   43,    4,    4,
            4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,    4,    4,    4,    4,  643,  643,
          643,  666,  666,  354,  173,  980,  203, 1048, 1048, 1048,
         1048, 1048, 1048, 1048, 1048, 1048,  665,  339,  164,  164,
            7,    7,    7,    7,    7,   50,  369,  583,  -23,  -23,
          -23,  -23,  448,  605,  497,  260,  397,  434,   54,  394,
          593,  593,  316,  316,  415,  415,  316,  316,  316,  442,
          442,  252,  252,  252,  252,  318,  455,  433,  391,  742,
           53,   53,   53,   53,  742,  742,  742,  742,  734, 1088,
          742,  742,  742,  722,  781,  781,  926,  551,  551,  781,
          536,   -3,   -3,  536,   63,   -3,   67,  576,  335,  756,
          115,    9,  335,  535,  656,  501,  185,  821,  568,  821,
         1032,  424,  776,  776,  426,  753,  729,  867, 1063, 1049,
          799, 1080,  810, 1081,  -66,  -58,  728, 1031, 1031, 1031,
         1031, 1031, 1031, 1031, 1031, 1031, 1031, 1031, 1089,  402,
         1032,  130, 1089, 1089, 1089,  402,  402,  402,  402,  402,
          402,  402,  402,  402,  402,  603,  130,  544,  554,  130,
          804,  402,   18,  808,   18,   18,   18,   18,   18,   18,
           18,   18,   18,   18,  762,  157,   18,   36,  124,  124,
          196,   37,  124,  124,  124,  124,   18,   18,   18,   18,
          568,  784,  797,  600,  820,  143,  784,  784,  784,  122,
          135,  204,  139,  760,  785,  467,  775,  775,  787,  895,
          895,  775,  768,  775,  787,  913,  775,  775,  895,  895,
          793,  158,  550,  472,  524,  569,  895,  346,  775,  775,
          775,  775,  816,  575,  775,  271,  171,  775,  775,  816,
          801,  766,   58,  798,  895,  895,  895,  816,  505,  798,
          798,  798,  819,  824,  761,  765,  383,  349,  607,  138,
          807,  765,  765,  775,  532,  761,  765,  761,  765,  759,
          765,  765,  765,  761,  765,  768,  498,  765,  714,  586,
           75,  765,    6,  915,  916,  726,  917,  906,  918,  965,
          919,  923, 1053,  894,  931,  912,  924,  966,  903,  896,
          780,  701,  703,  815,  754,  893,  777,  777,  777,  888,
          777,  777,  777,  777,  777,  777,  777,  777,  701,  868,
          823,  794,  934,  711,  712, 1011,  730,  795,  963,  933,
         1013,  925,  758,  713,  977,  935,  757, 1047,  936,  940,
          986, 1014,  828, 1017,  979,  790, 1064, 1065,  869,  946,
         1054,  777,  915,  923,  727,  912,  924,  903,  896,  752,
          748,  746,  747,  745,  744,  739,  740,  763, 1018,  887,
          879,  870,  945,  891,  701,  871,  971,  874,  990,  992,
         1050,  805,  792,  875, 1066,  952,  953,  954, 1055, 1019,
         1056,  773,  973,  817,  994,  812, 1067,  996,  997,  999,
         1000, 1057, 1068, 1058,  885, 1059,  832,  788,  928,  802,
         1069,  299,  791,  800,  806,  964,  436,  932, 1060, 1070,
         1071, 1001, 1002, 1006, 1072, 1073,  927,  834,  975,  796,
          976,  967,  835,  838,  577,  779, 1020,  786,  789,  778,
          624,  634, 1074, 1075, 1076,  930,  767,  772,  839,  845,
         1021,  743, 1022, 1077,  646,  846,  717, 1078, 1012,  718,
          721,  652,  683,  681,  724,  774, 1061,  818,  811,  771,
          955,  721,  770,  849, 1079,  852,  855,  856, 1007,  860,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          456,  456,  456,  456,  456,  456,  305,  305,  305,  305,
          305,  456,  456,  456,  456,  456,  456,  456,  305,  305,
            0,    0,  305,    0,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          289,  289,  289,  289,  289,  289,  289,  289,  289,  289,
          494,  494,  289,  289,  494,  289,  494,  494,  494,  494,
          494,  494,  494,  494,  494,    0,  289,  289,  289,  289,
          289,  289,  289,  289,  494,  793,  494,  442,  442,  442,
          442,  494,  494,  494,  -88,  -88,  442,  494,   63,  494,
          494,  494,  494,  494,  494,  494,  494,  494,    0,    0,
          494,  494,  494,  494,    0,    0,  130,   -3,  494,  768,
          768,  768,  768,  494,  494,  494,  494,   -3,   -3,  494,
          494,  494,    0,    0,    0,    0,  442,  442,    0,  130,
            0,    0,  130,    0,    0,  768,  768,  494,   63,  793,
          359,  494,    0,    0,    0,    0,  130,  768,  130,  402,
          775,   -3,   -3,  775,  402,  402,  124,   18,  359,  545,
          545,  545,  545,    0,    0,  568,  793,  793,  793,  793,
          793,  793,  793,  793,  793,  793,  793,  768,    0,  793,
            0,  768,  768,  768,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,  768,
            0,    0,  895,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,  913,    0,    0,    0,    0,    0,    0,
          768,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          777,  805,    0,  805,    0,  777,  777,  777,    0,    0,
            0,    0,  779,  743
    );

    protected array $actionDefault = array(
            3,32767,  102,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  100,32767,32767,32767,32767,  597,  597,
          597,  597,32767,32767,  254,  102,32767,32767,  470,  387,
          387,  387,32767,32767,  541,  541,  541,  541,  541,  541,
        32767,32767,32767,32767,32767,32767,  470,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  100,
        32767,32767,32767,   36,    7,    8,   10,   11,   49,   17,
          324,32767,32767,32767,32767,  102,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  590,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  474,  453,
          454,  456,  457,  386,  542,  596,  327,  593,  385,  145,
          339,  329,  242,  330,  258,  475,  259,  476,  479,  480,
          215,  287,  382,  149,  150,  417,  471,  419,  469,  473,
          418,  392,  398,  399,  400,  401,  402,  403,  404,  405,
          406,  407,  408,  409,  410,  390,  391,  472,  450,  449,
          448,32767,32767,  415,  416,32767,  420,32767,32767,32767,
        32767,32767,32767,32767,  102,32767,  389,  423,  421,  422,
          439,  440,  437,  438,  441,32767,32767,32767,  442,  443,
          444,  445,  316,32767,32767,  366,  364,  424,  316,  111,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  430,
          431,32767,32767,32767,32767,  535,  447,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          102,32767,  100,  537,  412,  414,  504,  425,  426,  393,
        32767,  511,32767,  102,32767,  513,32767,32767,32767,32767,
        32767,32767,32767,  536,32767,  543,  543,32767,  497,  100,
          195,32767,32767,  512,32767,  195,  195,32767,32767,32767,
        32767,32767,32767,32767,32767,  604,  497,  110,  110,  110,
          110,  110,  110,  110,  110,  110,  110,  110,32767,  195,
          110,32767,32767,32767,  100,  195,  195,  195,  195,  195,
          195,  195,  195,  195,  195,  190,32767,  268,  270,  102,
          558,  195,32767,  516,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  509,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          497,  435,  138,32767,  138,  543,  427,  428,  429,  499,
          543,  543,  543,  312,  289,32767,32767,32767,32767,  514,
          514,  100,  100,  100,  100,  509,32767,32767,32767,32767,
          111,   99,   99,   99,   99,   99,  103,  101,32767,32767,
        32767,32767,  223,   99,32767,  101,  101,32767,32767,  223,
          225,  212,  101,  227,32767,  562,  563,  223,  101,  227,
          227,  227,  247,  247,  486,  318,  101,   99,  101,  101,
          197,  318,  318,32767,  101,  486,  318,  486,  318,  199,
          318,  318,  318,  486,  318,32767,  101,  318,  214,   99,
           99,  318,32767,32767,32767,  499,32767,32767,32767,32767,
        32767,32767,32767,  222,32767,32767,32767,32767,32767,32767,
        32767,32767,  530,32767,  547,  560,  433,  434,  436,  545,
          458,  459,  460,  461,  462,  463,  464,  466,  592,32767,
          503,32767,32767,32767,  338,32767,  602,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  603,32767,  543,32767,32767,32767,
        32767,  432,    9,   74,  492,   42,   43,   51,   57,  520,
          521,  522,  523,  517,  518,  524,  519,32767,32767,  525,
          568,32767,32767,  544,  595,32767,32767,32767,32767,32767,
        32767,  138,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  530,32767,  136,32767,32767,32767,32767,
        32767,32767,32767,32767,  526,32767,32767,32767,  543,32767,
        32767,32767,32767,  314,  311,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  543,32767,32767,32767,32767,32767,  291,32767,  308,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  286,32767,32767,  381,
          499,  294,  296,  297,32767,32767,32767,32767,  360,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          152,  152,    3,    3,  341,  152,  152,  152,  341,  341,
          152,  341,  341,  341,  152,  152,  152,  152,  152,  152,
          280,  185,  262,  265,  247,  247,  152,  352,  152
    );

    protected array $goto = array(
          196,  196, 1034, 1065,  697,  431,  661,  621,  658,  319,
          706,  425,  314,  315,  335,  576,  430,  336,  432,  638,
          654,  655,  852,  672,  673,  674,  853,  167,  167,  167,
          167,  221,  197,  193,  193,  177,  179,  216,  193,  193,
          193,  193,  193,  194,  194,  194,  194,  194,  194,  188,
          189,  190,  191,  192,  218,  216,  219,  536,  537,  421,
          538,  540,  541,  542,  543,  544,  545,  546,  547, 1136,
          168,  169,  170,  195,  171,  172,  173,  166,  174,  175,
          176,  178,  215,  217,  220,  238,  243,  244,  246,  257,
          258,  259,  260,  261,  262,  263,  264,  268,  269,  270,
          271,  277,  289,  290,  317,  318,  426,  427,  428,  581,
          222,  223,  224,  225,  226,  227,  228,  229,  230,  231,
          232,  233,  234,  235,  236,  180,  237,  181,  198,  199,
          200,  239,  188,  189,  190,  191,  192,  218, 1136,  201,
          182,  183,  184,  202,  198,  185,  240,  203,  201,  165,
          204,  205,  186,  206,  207,  208,  187,  209,  210,  211,
          212,  213,  214,  855, 1232,  975,  279,  279,  279,  279,
          623,  623,  419,  351, 1269,  600, 1269, 1269, 1269, 1269,
         1269, 1269, 1269, 1269, 1269, 1287, 1287,  599, 1100, 1287,
          709, 1287, 1287, 1287, 1287, 1287, 1287, 1287, 1287, 1287,
          508,  700,  827, 1098,  458,  559,  552,  860,  833,  909,
          904,  905,  918,  861,  906,  858,  907,  908,  859, 1233,
         1234,  912,  500,  886,  501,  252,  252,  843, 1107, 1108,
          507, 1087, 1082, 1083, 1084,  341,  552,  559,  568,  569,
          344,  579,  602,  616,  617, 1235, 1295, 1296,  833,  440,
          833,   22,  250,  250,  250,  250,  245,  253,  694,  573,
         1237,  829, 1237,  893,  851,  893,  893, 1034, 1034, 1237,
          694,  349,  342, 1034,  694, 1034, 1034, 1034, 1034, 1034,
         1034, 1034, 1034, 1034,  848, 1327, 1034, 1034, 1034, 1034,
         1319, 1319, 1319, 1319, 1237,  343,  342, 1040, 1039, 1237,
         1237, 1237, 1237,  868,  996, 1237, 1237, 1237,  913,  355,
          914,  354,  354,  354,  354,  466,  466,  479,  880,  355,
          355,  867,  394,  926,  466,  481,  571,  927,  967,  410,
          705,  942,  355,  355,  942,  848,  355,  660, 1354,  609,
          624,  627,  628,  629,  630,  651,  652,  653,  708,  554,
         1133, 1285, 1285,  355,  355, 1285, 1058, 1285, 1285, 1285,
         1285, 1285, 1285, 1285, 1285, 1285,  539,  539, 1185,  424,
          539,  611,  539,  539,  539,  539,  539,  539,  539,  539,
          539,  566,  682, 1337, 1337,  733,  637,  639, 1043, 1044,
          659,  476, 1312, 1313,  683,  687, 1010,  695,  704, 1006,
         1337, 1298,  438,  408,  409,  631,  633,  635,  670,    5,
          671,    6,  412,  413,  414,  337,  684, 1340, 1340,  415,
          325,  309,  686,  347,  352,  353,  553,  563,  450,  450,
          450,  553, 1309,  563, 1309,  666,  397,  462,  845, 1314,
         1315, 1309,  548,  548,  548,  548,  873,  604,  469,  580,
          470,  471,  403,  554,  878,  848,  958, 1345, 1346,  577,
          614,  870,  550,  615,  550,  255,  255, 1321, 1321, 1321,
         1321,  550,  999, 1018,  477,  971, 1228,  732,  736,  881,
          869, 1070, 1074,  876,  882,  551, 1008, 1003, 1071, 1075,
          978,  980,    0, 1305, 1118,    0,  456,    0,    0,    0,
            0,  969,  969,  969,  969,    0,    0,  456,  963,  970,
            0,    0,    0,    0,  968,    0, 1230,    0,    0,    0,
          450,  450,  450,  450,  450,  450,  450,  450,  450,  450,
          450,  931, 1123,  450,    0, 1073, 1116,  885,  619, 1307,
         1307, 1073, 1216,  944, 1015,  433, 1217, 1220,  945, 1221,
            0,  433,  872,    0,  664,  994,    0, 1041, 1041,    0,
          866,    0,    0,    0,  665, 1052, 1048, 1049,    0,    0,
            0,    0, 1227,  324,  275,  324, 1037, 1037,  681,  952,
            0,    0, 1029, 1045, 1046,  396,  399,  560,  601,  605,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0, 1013, 1013
    );

    protected array $gotoCheck = array(
           42,   42,   73,  127,   73,   66,   66,   56,   56,   66,
            9,   66,   66,   66,   66,   66,   66,   66,   66,   66,
           86,   86,   26,   86,   86,   86,   27,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   15,   20,   49,   23,   23,   23,   23,
          108,  108,   43,   97,  108,  130,  108,  108,  108,  108,
          108,  108,  108,  108,  108,  170,  170,    8,    8,  170,
            8,  170,  170,  170,  170,  170,  170,  170,  170,  170,
            8,    8,    6,    8,   83,   76,   76,   15,   12,   15,
           15,   15,   15,   15,   15,   15,   15,   15,   15,   20,
           20,   15,  155,   45,  155,    5,    5,   20,  144,  144,
          155,   15,   15,   15,   15,   76,   76,   76,   76,   76,
           76,   76,   76,   76,   76,   20,   20,   20,   12,   83,
           12,   76,    5,    5,    5,    5,    5,    5,    7,  172,
           73,    7,   73,   25,   25,   25,   25,   73,   73,   73,
            7,  179,  168,   73,    7,   73,   73,   73,   73,   73,
           73,   73,   73,   73,   22,  181,   73,   73,   73,   73,
            9,    9,    9,    9,   73,  168,  168,  118,  118,   73,
           73,   73,   73,   35,  103,   73,   73,   73,   65,   14,
           65,   24,   24,   24,   24,  149,  149,   84,   35,   14,
           14,   35,   62,   73,  149,   84,  104,   73,   93,   93,
           93,    9,   14,   14,    9,   22,   14,   64,   14,   81,
           81,   81,   81,   81,   81,   81,   81,   81,   81,   14,
          150,  171,  171,   14,   14,  171,  114,  171,  171,  171,
          171,  171,  171,  171,  171,  171,  173,  173,  151,   13,
          173,   13,  173,  173,  173,  173,  173,  173,  173,  173,
          173,   48,  116,  182,  182,   48,   48,   48,  119,  119,
           48,  176,  176,  176,   48,   48,   48,   48,   48,   48,
          182,   14,  113,   82,   82,   85,   85,   85,   82,   46,
           82,   46,   82,   82,   82,   29,   82,  182,  182,   82,
          169,  169,   14,   82,   97,   97,    9,    9,   23,   23,
           23,    9,  130,    9,  130,  120,    9,    9,   18,  178,
          178,  130,  107,  107,  107,  107,   39,  107,    9,    9,
            9,    9,   28,   14,    9,   22,   92,    9,    9,    2,
            2,   37,   19,   80,   19,    5,    5,  130,  130,  130,
          130,   19,   50,  110,  157,   50,  160,   50,   99,   16,
           16,   16,   16,    9,   41,   50,   50,   50,  129,  132,
           16,   96,   -1,  130,  147,   -1,   19,   -1,   -1,   -1,
           -1,   19,   19,   19,   19,   -1,   -1,   19,   19,   19,
           -1,   -1,   -1,   -1,   16,   -1,   14,   -1,   -1,   -1,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   17,   17,   23,   -1,  130,   16,   16,   17,  130,
          130,  130,   79,   79,   17,  117,   79,   79,   79,   79,
           -1,  117,   17,   -1,   17,   17,   -1,  117,  117,   -1,
           17,   -1,   -1,   -1,  117,  117,  117,  117,   -1,   -1,
           -1,   -1,   17,   24,   24,   24,   89,   89,   89,   89,
           -1,   -1,   89,   89,   89,   59,   59,   59,   59,   59,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,  107,  107
    );

    protected array $gotoBase = array(
            0,    0, -253,    0,    0,  224,  182,  251,  179,  -10,
            0,    0,  -89,   32,   11, -185,   27,   66,  128,  197,
         -229,    0,    5,  163,  308,  260,   18,   22,  115,  118,
            0,    0,    0,    0,    0,  -68,    0,  122,    0,  123,
            0,   43,   -1,  153,    0,  200, -327,    0, -330,  147,
          460,    0,    0,    0,    0,    0,  -33,    0,    0,  540,
            0,    0,  280,    0,   95,  294, -236,    0,    0,    0,
            0,    0,    0,   -5,    0,    0, -140,    0,    0,  134,
          119,  -19,  -88,  -75, -152,  -74, -698,    0,    0,  296,
            0,    0,  127,   23,    0,    0,   48, -310,    0,   71,
            0,    0,    0,  269,  283,    0,    0,  414,  -71,    0,
          103,    0,    0,  124,   83,    0,  100,  273,   17,  104,
          144,    0,    0,    0,    0,    0,    0,    1,    0,  114,
          167,    0,   47,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  -47,    0,    0,   50,    0,  281,
          105,   94,    0,    0,    0, -273,    0,   34,    0,    0,
          107,    0,    0,    0,    0,    0,    0,    0,  -26,   99,
          -56,  110,  230,  125,    0,    0,   90,    0,   67,  241,
            0,  254,   75,    0,    0
    );

    protected array $gotoDefault = array(
        -32768,  512,  740,    4,  741,  935,  816,  825,  597,  530,
          707,  348,  625,  422, 1303,  911, 1122,  578,  844, 1246,
         1254,  457,  847,  330,  730,  923,  894,  895,  400,  386,
          392,  398,  649,  626,  494,  879,  453,  871,  486,  874,
          452,  883,  164,  418,  510,  887,    3,  890,  557,  921,
          973,  387,  898,  388,  677,  900,  562,  902,  903,  395,
          401,  402, 1127,  570,  622,  915,  256,  564,  916,  385,
          917,  925,  390,  393,  688,  465,  505,  499,  411, 1102,
          565,  608,  646,  447,  473,  620,  632,  618,  480,  434,
          416,  329,  957,  965,  487,  463,  979,  350,  987,  738,
         1135,  640,  489,  995,  641, 1002, 1005,  531,  532,  478,
         1017,  272, 1020,  490,   19,  667, 1031, 1032,  668,  642,
         1054,  643,  669,  644, 1056,  472,  598, 1064,  454, 1072,
         1291,  455, 1076,  266, 1079,  278,  417,  435, 1085, 1086,
            9, 1092,  698,  699,   11,  276,  509, 1117,  689,  451,
         1134,  439, 1204, 1206,  558,  491, 1224, 1223,  680,  506,
         1229,  448, 1294,  449,  533,  474,  316,  534, 1338,  308,
          333,  313,  549,  295,  334,  535,  475, 1300, 1308,  331,
           31, 1328, 1339,  575,  613
    );

    protected array $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    7,    7,    7,
            7,    7,    7,    7,    7,    8,    8,    9,   10,   11,
           11,   11,   12,   12,   13,   13,   14,   15,   15,   16,
           16,   17,   17,   18,   18,   21,   21,   22,   23,   23,
           24,   24,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,   29,   29,   30,   30,   32,   34,   34,
           28,   36,   36,   33,   38,   38,   35,   35,   37,   37,
           39,   39,   31,   40,   40,   41,   43,   44,   44,   45,
           45,   46,   46,   48,   47,   47,   47,   47,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   25,   25,   50,   69,   69,   72,   72,   71,
           70,   70,   63,   75,   75,   76,   76,   77,   77,   78,
           78,   79,   79,   80,   80,   26,   26,   27,   27,   27,
           27,   27,   88,   88,   90,   90,   83,   83,   91,   91,
           92,   92,   92,   84,   84,   87,   87,   85,   85,   93,
           94,   94,   57,   57,   65,   65,   68,   68,   68,   67,
           95,   95,   96,   58,   58,   58,   58,   97,   97,   98,
           98,   99,   99,  100,  101,  101,  102,  102,  103,  103,
           55,   55,   51,   51,  105,   53,   53,  106,   52,   52,
           54,   54,   64,   64,   64,   64,   81,   81,  109,  109,
          111,  111,  112,  112,  112,  112,  110,  110,  110,  114,
          114,  114,  114,   89,   89,  117,  117,  117,  118,  118,
          115,  115,  119,  119,  121,  121,  122,  122,  116,  123,
          123,  120,  124,  124,  124,  124,  113,  113,   82,   82,
           82,   20,   20,   20,  126,  125,  125,  127,  127,  127,
          127,   60,  128,  128,  129,   61,  131,  131,  132,  132,
          133,  133,   86,  134,  134,  134,  134,  134,  134,  134,
          139,  139,  140,  140,  141,  141,  141,  141,  141,  142,
          143,  143,  138,  138,  135,  135,  137,  137,  145,  145,
          144,  144,  144,  144,  144,  144,  144,  136,  146,  146,
          148,  147,  147,   62,  104,  149,  149,   56,   56,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,  156,  150,  150,  155,  155,  158,  159,
          159,  160,  161,  162,  162,  162,  162,   19,   19,   73,
           73,   73,   73,  151,  151,  151,  151,  164,  164,  152,
          152,  154,  154,  154,  157,  157,  170,  170,  170,  170,
          170,  170,  170,  170,  170,  171,  171,  171,  108,  173,
          173,  173,  173,  153,  153,  153,  153,  153,  153,  153,
          153,   59,   59,  167,  167,  167,  167,  174,  174,  163,
          163,  163,  175,  175,  175,  175,  175,  175,   74,   74,
           66,   66,   66,   66,  130,  130,  130,  130,  178,  177,
          166,  166,  166,  166,  166,  166,  166,  165,  165,  165,
          176,  176,  176,  176,  107,  172,  180,  180,  179,  179,
          181,  181,  181,  181,  181,  181,  181,  181,  169,  169,
          169,  169,  168,  183,  182,  182,  182,  182,  182,  182,
          182,  182,  184,  184,  184,  184
    );

    protected array $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    0,
            1,    0,    1,    1,    2,    1,    3,    4,    1,    2,
            0,    1,    1,    1,    1,    4,    3,    5,    4,    3,
            4,    2,    3,    1,    1,    7,    6,    2,    3,    1,
            2,    3,    1,    2,    3,    1,    1,    3,    1,    3,
            1,    2,    2,    3,    1,    3,    2,    3,    1,    3,
            3,    2,    0,    1,    1,    1,    1,    1,    3,    7,
           10,    5,    7,    9,    5,    3,    3,    3,    3,    3,
            3,    1,    2,    5,    7,    9,    6,    5,    6,    3,
            2,    1,    1,    1,    1,    0,    2,    1,    3,    8,
            0,    4,    2,    1,    3,    0,    1,    0,    1,    0,
            1,    3,    1,    1,    1,    8,    9,    7,    8,    7,
            6,    8,    0,    2,    0,    2,    1,    2,    1,    2,
            1,    1,    1,    0,    2,    0,    2,    0,    2,    2,
            1,    3,    1,    4,    1,    4,    1,    1,    4,    2,
            1,    3,    3,    3,    4,    4,    5,    0,    2,    4,
            3,    1,    1,    7,    0,    2,    1,    3,    3,    4,
            1,    4,    0,    2,    5,    0,    2,    6,    0,    2,
            0,    3,    1,    2,    1,    1,    2,    0,    1,    3,
            0,    2,    1,    1,    1,    1,    6,    8,    6,    1,
            2,    1,    1,    1,    1,    1,    1,    1,    1,    3,
            3,    3,    1,    3,    3,    3,    3,    3,    1,    3,
            3,    1,    1,    2,    1,    1,    0,    1,    0,    2,
            2,    2,    4,    3,    1,    1,    3,    1,    2,    2,
            3,    2,    3,    1,    1,    2,    3,    1,    1,    3,
            2,    0,    1,    5,    5,    6,   10,    3,    5,    1,
            1,    3,    0,    2,    4,    5,    4,    4,    4,    3,
            1,    1,    1,    1,    1,    1,    0,    1,    1,    2,
            1,    1,    1,    1,    1,    1,    1,    2,    1,    3,
            1,    1,    3,    2,    2,    3,    1,    0,    1,    1,
            3,    3,    3,    4,    4,    1,    1,    2,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    2,    2,    2,    2,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    2,    2,    2,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    5,    4,
            3,    4,    4,    2,    2,    4,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    2,    1,    3,    2,
            1,    2,    4,    2,    2,    8,    9,    8,    9,    9,
           10,    9,   10,    8,    3,    2,    0,    4,    2,    1,
            3,    2,    1,    2,    2,    2,    4,    1,    1,    1,
            1,    1,    1,    1,    1,    3,    1,    1,    1,    0,
            3,    0,    1,    1,    0,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    3,    5,    3,    3,    4,
            1,    1,    3,    1,    1,    1,    1,    1,    3,    2,
            3,    0,    1,    1,    3,    1,    1,    1,    1,    1,
            3,    1,    1,    4,    4,    1,    4,    4,    0,    1,
            1,    1,    3,    3,    1,    4,    2,    2,    1,    3,
            1,    4,    4,    3,    3,    3,    3,    1,    3,    1,
            1,    3,    1,    1,    4,    1,    1,    1,    3,    1,
            1,    2,    1,    3,    4,    3,    2,    0,    2,    2,
            1,    2,    1,    1,    1,    4,    3,    3,    3,    3,
            6,    3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks(): void {
        $this->reduceCallbacks = [
            0 => null,
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; } $this->semValue = $this->semStack[$stackPos-(2-1)];;
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $nop = $this->maybeCreateZeroLengthNop($this->tokenPos);;
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => null,
            6 => null,
            7 => null,
            8 => null,
            9 => null,
            10 => null,
            11 => null,
            12 => null,
            13 => null,
            14 => null,
            15 => null,
            16 => null,
            17 => null,
            18 => null,
            19 => null,
            20 => null,
            21 => null,
            22 => null,
            23 => null,
            24 => null,
            25 => null,
            26 => null,
            27 => null,
            28 => null,
            29 => null,
            30 => null,
            31 => null,
            32 => null,
            33 => null,
            34 => null,
            35 => null,
            36 => null,
            37 => null,
            38 => null,
            39 => null,
            40 => null,
            41 => null,
            42 => null,
            43 => null,
            44 => null,
            45 => null,
            46 => null,
            47 => null,
            48 => null,
            49 => null,
            50 => null,
            51 => null,
            52 => null,
            53 => null,
            54 => null,
            55 => null,
            56 => null,
            57 => null,
            58 => null,
            59 => null,
            60 => null,
            61 => null,
            62 => null,
            63 => null,
            64 => null,
            65 => null,
            66 => null,
            67 => null,
            68 => null,
            69 => null,
            70 => null,
            71 => null,
            72 => null,
            73 => null,
            74 => null,
            75 => null,
            76 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; if ($this->semValue === "<?=") $this->emitError(new Error('Cannot use "<?=" as an identifier', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])));
            },
            77 => null,
            78 => null,
            79 => null,
            80 => null,
            81 => null,
            82 => null,
            83 => null,
            84 => null,
            85 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            88 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            89 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            90 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            92 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            93 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            94 => null,
            95 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            96 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            97 => function ($stackPos) {
                 /* nothing */
            },
            98 => function ($stackPos) {
                 /* nothing */
            },
            99 => function ($stackPos) {
                 /* nothing */
            },
            100 => function ($stackPos) {
                 $this->emitError(new Error('A trailing comma is not allowed here', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])));
            },
            101 => null,
            102 => null,
            103 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(1-1)], [], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            104 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            105 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            106 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            107 => function ($stackPos) {
                 $this->semValue = new Node\AttributeGroup($this->semStack[$stackPos-(4-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            108 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            109 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            110 => function ($stackPos) {
                 $this->semValue = [];
            },
            111 => null,
            112 => null,
            113 => null,
            114 => null,
            115 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->handleHaltCompiler(), $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            118 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            119 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            120 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            121 => null,
            122 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            123 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            124 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            125 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->semStack[$stackPos-(7-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            126 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            127 => null,
            128 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            129 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            130 => null,
            131 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            132 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            133 => null,
            134 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            135 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            136 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            137 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            138 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            139 => function ($stackPos) {
                 $this->semValue = new Node\UseItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            140 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            141 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            142 => null,
            143 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            144 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            145 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            146 => null,
            147 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            148 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            149 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)],  $this->tokenEndStack[$stackPos-(3-1)])), $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            150 => function ($stackPos) {
                 $this->semValue = new Node\Const_(new Node\Identifier($this->semStack[$stackPos-(3-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)],  $this->tokenEndStack[$stackPos-(3-1)])), $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            151 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; } $this->semValue = $this->semStack[$stackPos-(2-1)];;
            },
            152 => function ($stackPos) {
                 $this->semValue = array();
            },
            153 => function ($stackPos) {
                 $nop = $this->maybeCreateZeroLengthNop($this->tokenPos);;
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            154 => null,
            155 => null,
            156 => null,
            157 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            158 => function ($stackPos) {
                 $this->semValue = new Stmt\Block($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            159 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(7-3)], ['stmts' => $this->semStack[$stackPos-(7-5)], 'elseifs' => $this->semStack[$stackPos-(7-6)], 'else' => $this->semStack[$stackPos-(7-7)]], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(10-3)], ['stmts' => $this->semStack[$stackPos-(10-6)], 'elseifs' => $this->semStack[$stackPos-(10-7)], 'else' => $this->semStack[$stackPos-(10-8)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            },
            161 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            162 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(7-5)], $this->semStack[$stackPos-(7-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            163 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            164 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            165 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            167 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            169 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            170 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            171 => function ($stackPos) {

        $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
        $this->semValue->setAttribute('hasLeadingNewline', $this->inlineHtmlHasLeadingNewline($stackPos-(1-1)));

            },
            172 => function ($stackPos) {
                 $this->semValue = new Stmt\Expression($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            173 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            174 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            175 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            176 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(6-3)], new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(6-4)],  $this->tokenEndStack[$stackPos-(6-4)])), ['stmts' => $this->semStack[$stackPos-(6-6)]], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            177 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            178 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos])); $this->checkTryCatch($this->semValue);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            181 => function ($stackPos) {
                 $this->semValue = null; /* means: no statement */
            },
            182 => null,
            183 => function ($stackPos) {
                 $this->semValue = $this->maybeCreateNop($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]);
            },
            184 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $this->semValue = $this->semStack[$stackPos-(1-1)]->stmts; } else if ($this->semStack[$stackPos-(1-1)] === null) { $this->semValue = []; } else { $this->semValue = [$this->semStack[$stackPos-(1-1)]]; };
            },
            185 => function ($stackPos) {
                 $this->semValue = array();
            },
            186 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            187 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            188 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            189 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            190 => function ($stackPos) {
                 $this->semValue = null;
            },
            191 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            192 => null,
            193 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            194 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            195 => function ($stackPos) {
                 $this->semValue = false;
            },
            196 => function ($stackPos) {
                 $this->semValue = true;
            },
            197 => function ($stackPos) {
                 $this->semValue = false;
            },
            198 => function ($stackPos) {
                 $this->semValue = true;
            },
            199 => function ($stackPos) {
                 $this->semValue = false;
            },
            200 => function ($stackPos) {
                 $this->semValue = true;
            },
            201 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            202 => function ($stackPos) {
                 $this->semValue = [];
            },
            203 => null,
            204 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            205 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(8-3)], ['byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-5)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            206 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(9-4)], ['byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            207 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(7-2)], ['type' => $this->semStack[$stackPos-(7-1)], 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            $this->checkClass($this->semValue, $stackPos-(7-2));
            },
            208 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(8-3)], ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            $this->checkClass($this->semValue, $stackPos-(8-3));
            },
            209 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(7-3)], ['extends' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => $this->semStack[$stackPos-(7-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            $this->checkInterface($this->semValue, $stackPos-(7-3));
            },
            210 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(6-3)], ['stmts' => $this->semStack[$stackPos-(6-5)], 'attrGroups' => $this->semStack[$stackPos-(6-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            211 => function ($stackPos) {
                 $this->semValue = new Stmt\Enum_($this->semStack[$stackPos-(8-3)], ['scalarType' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            $this->checkEnum($this->semValue, $stackPos-(8-3));
            },
            212 => function ($stackPos) {
                 $this->semValue = null;
            },
            213 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            214 => function ($stackPos) {
                 $this->semValue = null;
            },
            215 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            216 => function ($stackPos) {
                 $this->semValue = 0;
            },
            217 => null,
            218 => null,
            219 => function ($stackPos) {
                 $this->checkClassModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            220 => function ($stackPos) {
                 $this->semValue = Modifiers::ABSTRACT;
            },
            221 => function ($stackPos) {
                 $this->semValue = Modifiers::FINAL;
            },
            222 => function ($stackPos) {
                 $this->semValue = Modifiers::READONLY;
            },
            223 => function ($stackPos) {
                 $this->semValue = null;
            },
            224 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            225 => function ($stackPos) {
                 $this->semValue = array();
            },
            226 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            227 => function ($stackPos) {
                 $this->semValue = array();
            },
            228 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            229 => null,
            230 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            231 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            232 => null,
            233 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            234 => null,
            235 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            236 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $this->semValue = $this->semStack[$stackPos-(1-1)]->stmts; } else if ($this->semStack[$stackPos-(1-1)] === null) { $this->semValue = []; } else { $this->semValue = [$this->semStack[$stackPos-(1-1)]]; };
            },
            237 => function ($stackPos) {
                 $this->semValue = null;
            },
            238 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            239 => null,
            240 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            241 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            242 => function ($stackPos) {
                 $this->semValue = new Node\DeclareItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            243 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            244 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            245 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            246 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            247 => function ($stackPos) {
                 $this->semValue = array();
            },
            248 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            249 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            250 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            251 => null,
            252 => null,
            253 => function ($stackPos) {
                 $this->semValue = new Expr\Match_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->getAttributes($this->tokenStartStack[$stackPos-(7-1)], $this->tokenEndStack[$stackPos]));
            },
            254 => function ($stackPos) {
                 $this->semValue = [];
            },
            255 => null,
            256 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            257 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            258 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            259 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm(null, $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            260 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            261 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            262 => function ($stackPos) {
                 $this->semValue = array();
            },
            263 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            264 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            265 => function ($stackPos) {
                 $this->semValue = array();
            },
            266 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            267 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-6)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos])); $this->fixupAlternativeElse($this->semValue);
            },
            268 => function ($stackPos) {
                 $this->semValue = null;
            },
            269 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            270 => function ($stackPos) {
                 $this->semValue = null;
            },
            271 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->fixupAlternativeElse($this->semValue);
            },
            272 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            273 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            274 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            275 => function ($stackPos) {
                 $this->semValue = array($this->fixupArrayDestructuring($this->semStack[$stackPos-(1-1)]), false);
            },
            276 => null,
            277 => function ($stackPos) {
                 $this->semValue = array();
            },
            278 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            279 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            280 => function ($stackPos) {
                 $this->semValue = 0;
            },
            281 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            282 => function ($stackPos) {
                 $this->semValue = Modifiers::PUBLIC;
            },
            283 => function ($stackPos) {
                 $this->semValue = Modifiers::PROTECTED;
            },
            284 => function ($stackPos) {
                 $this->semValue = Modifiers::PRIVATE;
            },
            285 => function ($stackPos) {
                 $this->semValue = Modifiers::READONLY;
            },
            286 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-6)], null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            $this->checkParam($this->semValue);
            },
            287 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-8)], $this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(8-2)], $this->semStack[$stackPos-(8-1)]);
            $this->checkParam($this->semValue);
            },
            288 => function ($stackPos) {
                 $this->semValue = new Node\Param(new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos])), null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            },
            289 => null,
            290 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            291 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            292 => null,
            293 => null,
            294 => function ($stackPos) {
                 $this->semValue = new Node\Name('static', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            295 => function ($stackPos) {
                 $this->semValue = $this->handleBuiltinTypes($this->semStack[$stackPos-(1-1)]);
            },
            296 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            297 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            298 => null,
            299 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            300 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            301 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            302 => null,
            303 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            304 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            305 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            306 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            307 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            308 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            309 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            310 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            311 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            312 => null,
            313 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            314 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            315 => null,
            316 => function ($stackPos) {
                 $this->semValue = null;
            },
            317 => null,
            318 => function ($stackPos) {
                 $this->semValue = null;
            },
            319 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            320 => function ($stackPos) {
                 $this->semValue = null;
            },
            321 => function ($stackPos) {
                 $this->semValue = array();
            },
            322 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            323 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-2)]);
            },
            324 => function ($stackPos) {
                 $this->semValue = new Node\VariadicPlaceholder($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            325 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            326 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            327 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            328 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            329 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            330 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(3-3)], false, false, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(3-1)]);
            },
            331 => null,
            332 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            333 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            334 => null,
            335 => null,
            336 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            337 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            338 => function ($stackPos) {
                 $this->semValue = new Node\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            339 => function ($stackPos) {
                 $this->semValue = new Node\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            340 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; } else { $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            341 => function ($stackPos) {
                 $this->semValue = array();
            },
            342 => function ($stackPos) {
                 $nop = $this->maybeCreateZeroLengthNop($this->tokenPos);;
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            343 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-1)]);
            $this->checkProperty($this->semValue, $stackPos-(5-2));
            },
            344 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(5-1)]);
            $this->checkClassConst($this->semValue, $stackPos-(5-2));
            },
            345 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]), $this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-4)]);
            $this->checkClassConst($this->semValue, $stackPos-(6-2));
            },
            346 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(10-5)], ['type' => $this->semStack[$stackPos-(10-2)], 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-7)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            $this->checkClassMethod($this->semValue, $stackPos-(10-2));
            },
            347 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            348 => function ($stackPos) {
                 $this->semValue = new Stmt\EnumCase($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            349 => function ($stackPos) {
                 $this->semValue = null; /* will be skipped */
            },
            350 => function ($stackPos) {
                 $this->semValue = array();
            },
            351 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            352 => function ($stackPos) {
                 $this->semValue = array();
            },
            353 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            354 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            355 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            356 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            357 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            358 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            359 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            360 => null,
            361 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            362 => function ($stackPos) {
                 $this->semValue = null;
            },
            363 => null,
            364 => null,
            365 => function ($stackPos) {
                 $this->semValue = 0;
            },
            366 => function ($stackPos) {
                 $this->semValue = 0;
            },
            367 => null,
            368 => null,
            369 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            370 => function ($stackPos) {
                 $this->semValue = Modifiers::PUBLIC;
            },
            371 => function ($stackPos) {
                 $this->semValue = Modifiers::PROTECTED;
            },
            372 => function ($stackPos) {
                 $this->semValue = Modifiers::PRIVATE;
            },
            373 => function ($stackPos) {
                 $this->semValue = Modifiers::STATIC;
            },
            374 => function ($stackPos) {
                 $this->semValue = Modifiers::ABSTRACT;
            },
            375 => function ($stackPos) {
                 $this->semValue = Modifiers::FINAL;
            },
            376 => function ($stackPos) {
                 $this->semValue = Modifiers::READONLY;
            },
            377 => null,
            378 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            379 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            380 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            381 => function ($stackPos) {
                 $this->semValue = new Node\PropertyItem($this->semStack[$stackPos-(1-1)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            382 => function ($stackPos) {
                 $this->semValue = new Node\PropertyItem($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            383 => null,
            384 => null,
            385 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            386 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            387 => function ($stackPos) {
                 $this->semValue = array();
            },
            388 => null,
            389 => null,
            390 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            391 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->fixupArrayDestructuring($this->semStack[$stackPos-(3-1)]), $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            392 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            393 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            394 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            if (!$this->phpVersion->allowsAssignNewByReference()) {
                $this->emitError(new Error('Cannot assign new by reference', $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos])));
            }

            },
            395 => null,
            396 => null,
            397 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            399 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            400 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            401 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            402 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            403 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            405 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            410 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            413 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            414 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            415 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            416 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            417 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            418 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            419 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            420 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            421 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            422 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            423 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            424 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            429 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            430 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            431 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            432 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            433 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            434 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            435 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            436 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            437 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            438 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            439 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            440 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            441 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            442 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            443 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            444 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            445 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            446 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            447 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            448 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            449 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            450 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            451 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            455 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            456 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            458 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            459 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]);
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            462 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            464 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            465 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]);
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            466 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            467 => null,
            468 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            469 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            470 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            471 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            472 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            473 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            474 => function ($stackPos) {
                 $this->semValue = new Expr\Throw_($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'returnType' => $this->semStack[$stackPos-(8-6)], 'expr' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'uses' => $this->semStack[$stackPos-(8-6)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos]));
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            479 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            480 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-8)], 'expr' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            },
            481 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(9-1)], $this->tokenEndStack[$stackPos]));
            },
            482 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'uses' => $this->semStack[$stackPos-(10-8)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(10-1)], $this->tokenEndStack[$stackPos]));
            },
            483 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->getAttributes($this->tokenStartStack[$stackPos-(8-1)], $this->tokenEndStack[$stackPos])), $this->semStack[$stackPos-(8-3)]);
            $this->checkClass($this->semValue[0], -1);
            },
            484 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            485 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            486 => function ($stackPos) {
                 $this->semValue = array();
            },
            487 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            488 => null,
            489 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            490 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            491 => function ($stackPos) {
                 $this->semValue = new Node\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            492 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            493 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            494 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            495 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            496 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            497 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            498 => null,
            499 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            500 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            501 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            502 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            503 => null,
            504 => null,
            505 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            506 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            507 => null,
            508 => null,
            509 => function ($stackPos) {
                 $this->semValue = null;
            },
            510 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            511 => function ($stackPos) {
                 $this->semValue = array();
            },
            512 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]); foreach ($this->semValue as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $this->phpVersion->supportsUnicodeEscapes()); } };
            },
            513 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $this->phpVersion->supportsUnicodeEscapes()); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            514 => function ($stackPos) {
                 $this->semValue = array();
            },
            515 => null,
            516 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            517 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            518 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            519 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            520 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            521 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            522 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            523 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            524 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            525 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            526 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(5-1)], $this->tokenEndStack[$stackPos]));
            },
            527 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(3-3)],  $this->tokenEndStack[$stackPos-(3-3)])), $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            528 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            529 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            $this->createdArrays->attach($this->semValue);
            },
            530 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->createdArrays->attach($this->semValue);
            },
            531 => function ($stackPos) {
                 $this->semValue = Scalar\String_::fromString($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]), $this->phpVersion->supportsUnicodeEscapes());
            },
            532 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]); $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', $this->phpVersion->supportsUnicodeEscapes()); } }; $this->semValue = new Scalar\InterpolatedString($this->semStack[$stackPos-(3-2)], $attrs);
            },
            533 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]), $this->phpVersion->allowsInvalidOctals());
            },
            534 => function ($stackPos) {
                 $this->semValue = Scalar\Float_::fromString($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            535 => null,
            536 => null,
            537 => null,
            538 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]), $this->getAttributes($this->tokenStartStack[$stackPos-(3-3)],  $this->tokenEndStack[$stackPos-(3-3)]), true);
            },
            539 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]), $this->getAttributes($this->tokenStartStack[$stackPos-(2-2)],  $this->tokenEndStack[$stackPos-(2-2)]), true);
            },
            540 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]), $this->getAttributes($this->tokenStartStack[$stackPos-(3-3)],  $this->tokenEndStack[$stackPos-(3-3)]), true);
            },
            541 => function ($stackPos) {
                 $this->semValue = null;
            },
            542 => null,
            543 => null,
            544 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            545 => null,
            546 => null,
            547 => null,
            548 => null,
            549 => null,
            550 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            551 => null,
            552 => null,
            553 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            554 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            555 => null,
            556 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            557 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafeMethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            558 => function ($stackPos) {
                 $this->semValue = null;
            },
            559 => null,
            560 => null,
            561 => null,
            562 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            563 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            564 => null,
            565 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            566 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            567 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos])), $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            568 => function ($stackPos) {
                 $var = $this->semStack[$stackPos-(1-1)]->name; $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])) : $var;
            },
            569 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            570 => null,
            571 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            572 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            573 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            574 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            575 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            576 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            577 => null,
            578 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            579 => null,
            580 => null,
            581 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            582 => null,
            583 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos])); $this->errorState = 2;
            },
            584 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos])); $this->semValue->setAttribute('kind', Expr\List_::KIND_LIST);
            $this->postprocessList($this->semValue);
            },
            585 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $end = count($this->semValue)-1; if ($this->semValue[$end]->value instanceof Expr\Error) array_pop($this->semValue);
            },
            586 => null,
            587 => function ($stackPos) {
                 /* do nothing -- prevent default action of $$=$this->semStack[$1]. See $551. */
            },
            588 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            589 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            590 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            591 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            592 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            593 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            594 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            595 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            596 => function ($stackPos) {
                 $this->semValue = new Node\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]), true);
            },
            597 => function ($stackPos) {
                 /* Create an Error node now to remember the position. We'll later either report an error,
             or convert this into a null element, depending on whether this is a creation or destructuring context. */
          $attrs = $this->createEmptyElemAttributes($this->tokenPos);
          $this->semValue = new Node\ArrayItem(new Expr\Error($attrs), null, false, $attrs);
            },
            598 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            599 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            600 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            601 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            602 => function ($stackPos) {
                 $attrs = $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]); $attrs['rawValue'] = $this->semStack[$stackPos-(1-1)]; $this->semValue = new Node\InterpolatedStringPart($this->semStack[$stackPos-(1-1)], $attrs);
            },
            603 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            604 => null,
            605 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(4-1)], $this->tokenEndStack[$stackPos]));
            },
            606 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            607 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            608 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            609 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(3-1)], $this->tokenEndStack[$stackPos]));
            },
            610 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->getAttributes($this->tokenStartStack[$stackPos-(6-1)], $this->tokenEndStack[$stackPos]));
            },
            611 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            612 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            613 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->getAttributes($this->tokenStartStack[$stackPos-(1-1)], $this->tokenEndStack[$stackPos]));
            },
            614 => function ($stackPos) {
                 $this->semValue = $this->parseNumString('-' . $this->semStack[$stackPos-(2-2)], $this->getAttributes($this->tokenStartStack[$stackPos-(2-1)], $this->tokenEndStack[$stackPos]));
            },
            615 => null,
        ];
    }
}
